{"name": "for-in", "description": "Iterate over the own and inherited enumerable properties of an object, and return an object with properties that evaluate to true from the callback. Exit early by returning `false`. JavaScript/Node.js", "version": "1.0.2", "homepage": "https://github.com/jonschlinkert/for-in", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/for-in", "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.2.0"}, "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["arr-flatten", "collection-map", "for-own"]}, "reflinks": ["verb"], "lint": {"reflinks": true}}}