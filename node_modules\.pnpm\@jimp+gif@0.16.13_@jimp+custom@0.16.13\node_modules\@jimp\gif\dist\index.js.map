{"version": 3, "sources": ["../src/index.js"], "names": ["MIME_TYPE", "mime", "constants", "MIME_GIF", "decoders", "data", "gifObj", "GIF", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gifData", "<PERSON><PERSON><PERSON>", "alloc", "width", "height", "decodeAndBlitFrameRGBA", "encoders", "bitmap", "BitmapImage", "<PERSON><PERSON><PERSON><PERSON>", "quantizeDekker", "newFrame", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gifCodec", "GifCodec", "encodeGif", "then", "newGif", "buffer"], "mappings": ";;;;;;;;;;;AAAA;;AACA;;AAEA,IAAMA,SAAS,GAAG,WAAlB;;eAEe;AAAA,SAAO;AACpBC,IAAAA,IAAI,uCAAKD,SAAL,EAAiB,CAAC,KAAD,CAAjB,CADgB;AAGpBE,IAAAA,SAAS,EAAE;AACTC,MAAAA,QAAQ,EAAEH;AADD,KAHS;AAOpBI,IAAAA,QAAQ,uCACLJ,SADK,EACO,UAACK,IAAD,EAAU;AACrB,UAAMC,MAAM,GAAG,IAAIC,mBAAIC,SAAR,CAAkBH,IAAlB,CAAf;AACA,UAAMI,OAAO,GAAGC,MAAM,CAACC,KAAP,CAAaL,MAAM,CAACM,KAAP,GAAeN,MAAM,CAACO,MAAtB,GAA+B,CAA5C,CAAhB;AAEAP,MAAAA,MAAM,CAACQ,sBAAP,CAA8B,CAA9B,EAAiCL,OAAjC;AAEA,aAAO;AACLJ,QAAAA,IAAI,EAAEI,OADD;AAELG,QAAAA,KAAK,EAAEN,MAAM,CAACM,KAFT;AAGLC,QAAAA,MAAM,EAAEP,MAAM,CAACO;AAHV,OAAP;AAKD,KAZK,CAPY;AAsBpBE,IAAAA,QAAQ,uCACLf,SADK,EACO,UAACK,IAAD,EAAU;AACrB,UAAMW,MAAM,GAAG,IAAIC,oBAAJ,CAAgBZ,IAAI,CAACW,MAArB,CAAf;;AACAE,uBAAQC,cAAR,CAAuBH,MAAvB,EAA+B,GAA/B;;AACA,UAAMI,QAAQ,GAAG,IAAIC,iBAAJ,CAAaL,MAAb,CAAjB;AACA,UAAMM,QAAQ,GAAG,IAAIC,iBAAJ,EAAjB;AACA,aAAOD,QAAQ,CAACE,SAAT,CAAmB,CAACJ,QAAD,CAAnB,EAA+B,EAA/B,EAAmCK,IAAnC,CAAwC,UAACC,MAAD,EAAY;AACzD,eAAOA,MAAM,CAACC,MAAd;AACD,OAFM,CAAP;AAGD,KATK;AAtBY,GAAP;AAAA,C", "sourcesContent": ["import GIF from \"omggif\";\nimport { GifUtil, GifFrame, BitmapImage, GifCodec } from \"gifwrap\";\n\nconst MIME_TYPE = \"image/gif\";\n\nexport default () => ({\n  mime: { [MIME_TYPE]: [\"gif\"] },\n\n  constants: {\n    MIME_GIF: MIME_TYPE,\n  },\n\n  decoders: {\n    [MIME_TYPE]: (data) => {\n      const gifObj = new GIF.GifReader(data);\n      const gifData = Buffer.alloc(gifObj.width * gifObj.height * 4);\n\n      gifObj.decodeAndBlitFrameRGBA(0, gifData);\n\n      return {\n        data: gifData,\n        width: gifObj.width,\n        height: gifObj.height,\n      };\n    },\n  },\n\n  encoders: {\n    [MIME_TYPE]: (data) => {\n      const bitmap = new BitmapImage(data.bitmap);\n      GifUtil.quantizeDekker(bitmap, 256);\n      const newFrame = new GifFrame(bitmap);\n      const gifCodec = new GifCodec();\n      return gifCodec.encodeGif([newFrame], {}).then((newGif) => {\n        return newGif.buffer;\n      });\n    },\n  },\n});\n"], "file": "index.js"}