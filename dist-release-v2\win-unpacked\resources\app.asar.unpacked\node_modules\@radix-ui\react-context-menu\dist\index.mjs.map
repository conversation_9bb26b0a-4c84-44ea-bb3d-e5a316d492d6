{"version": 3, "sources": ["../src/context-menu.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as MenuPrimitive from '@radix-ui/react-menu';\nimport { createMenuScope } from '@radix-ui/react-menu';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\ntype Point = { x: number; y: number };\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenu\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTEXT_MENU_NAME = 'ContextMenu';\n\ntype ScopedProps<P> = P & { __scopeContextMenu?: Scope };\nconst [createContextMenuContext, createContextMenuScope] = createContextScope(CONTEXT_MENU_NAME, [\n  createMenuScope,\n]);\nconst useMenuScope = createMenuScope();\n\ntype ContextMenuContextValue = {\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  modal: boolean;\n};\n\nconst [ContextMenuProvider, useContextMenuContext] =\n  createContextMenuContext<ContextMenuContextValue>(CONTEXT_MENU_NAME);\n\ninterface ContextMenuProps {\n  children?: React.ReactNode;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  modal?: boolean;\n}\n\nconst ContextMenu: React.FC<ContextMenuProps> = (props: ScopedProps<ContextMenuProps>) => {\n  const { __scopeContextMenu, children, onOpenChange, dir, modal = true } = props;\n  const [open, setOpen] = React.useState(false);\n  const menuScope = useMenuScope(__scopeContextMenu);\n  const handleOpenChangeProp = useCallbackRef(onOpenChange);\n\n  const handleOpenChange = React.useCallback(\n    (open: boolean) => {\n      setOpen(open);\n      handleOpenChangeProp(open);\n    },\n    [handleOpenChangeProp]\n  );\n\n  return (\n    <ContextMenuProvider\n      scope={__scopeContextMenu}\n      open={open}\n      onOpenChange={handleOpenChange}\n      modal={modal}\n    >\n      <MenuPrimitive.Root\n        {...menuScope}\n        dir={dir}\n        open={open}\n        onOpenChange={handleOpenChange}\n        modal={modal}\n      >\n        {children}\n      </MenuPrimitive.Root>\n    </ContextMenuProvider>\n  );\n};\n\nContextMenu.displayName = CONTEXT_MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'ContextMenuTrigger';\n\ntype ContextMenuTriggerElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface ContextMenuTriggerProps extends PrimitiveSpanProps {\n  disabled?: boolean;\n}\n\nconst ContextMenuTrigger = React.forwardRef<ContextMenuTriggerElement, ContextMenuTriggerProps>(\n  (props: ScopedProps<ContextMenuTriggerProps>, forwardedRef) => {\n    const { __scopeContextMenu, disabled = false, ...triggerProps } = props;\n    const context = useContextMenuContext(TRIGGER_NAME, __scopeContextMenu);\n    const menuScope = useMenuScope(__scopeContextMenu);\n    const pointRef = React.useRef<Point>({ x: 0, y: 0 });\n    const virtualRef = React.useRef({\n      getBoundingClientRect: () => DOMRect.fromRect({ width: 0, height: 0, ...pointRef.current }),\n    });\n    const longPressTimerRef = React.useRef(0);\n    const clearLongPress = React.useCallback(\n      () => window.clearTimeout(longPressTimerRef.current),\n      []\n    );\n    const handleOpen = (event: React.MouseEvent | React.PointerEvent) => {\n      pointRef.current = { x: event.clientX, y: event.clientY };\n      context.onOpenChange(true);\n    };\n\n    React.useEffect(() => clearLongPress, [clearLongPress]);\n    React.useEffect(() => void (disabled && clearLongPress()), [disabled, clearLongPress]);\n\n    return (\n      <>\n        <MenuPrimitive.Anchor {...menuScope} virtualRef={virtualRef} />\n        <Primitive.span\n          data-state={context.open ? 'open' : 'closed'}\n          data-disabled={disabled ? '' : undefined}\n          {...triggerProps}\n          ref={forwardedRef}\n          // prevent iOS context menu from appearing\n          style={{ WebkitTouchCallout: 'none', ...props.style }}\n          // if trigger is disabled, enable the native Context Menu\n          onContextMenu={\n            disabled\n              ? props.onContextMenu\n              : composeEventHandlers(props.onContextMenu, (event) => {\n                  // clearing the long press here because some platforms already support\n                  // long press to trigger a `contextmenu` event\n                  clearLongPress();\n                  handleOpen(event);\n                  event.preventDefault();\n                })\n          }\n          onPointerDown={\n            disabled\n              ? props.onPointerDown\n              : composeEventHandlers(\n                  props.onPointerDown,\n                  whenTouchOrPen((event) => {\n                    // clear the long press here in case there's multiple touch points\n                    clearLongPress();\n                    longPressTimerRef.current = window.setTimeout(() => handleOpen(event), 700);\n                  })\n                )\n          }\n          onPointerMove={\n            disabled\n              ? props.onPointerMove\n              : composeEventHandlers(props.onPointerMove, whenTouchOrPen(clearLongPress))\n          }\n          onPointerCancel={\n            disabled\n              ? props.onPointerCancel\n              : composeEventHandlers(props.onPointerCancel, whenTouchOrPen(clearLongPress))\n          }\n          onPointerUp={\n            disabled\n              ? props.onPointerUp\n              : composeEventHandlers(props.onPointerUp, whenTouchOrPen(clearLongPress))\n          }\n        />\n      </>\n    );\n  }\n);\n\nContextMenuTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'ContextMenuPortal';\n\ntype MenuPortalProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Portal>;\ninterface ContextMenuPortalProps extends MenuPortalProps {}\n\nconst ContextMenuPortal: React.FC<ContextMenuPortalProps> = (\n  props: ScopedProps<ContextMenuPortalProps>\n) => {\n  const { __scopeContextMenu, ...portalProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return <MenuPrimitive.Portal {...menuScope} {...portalProps} />;\n};\n\nContextMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'ContextMenuContent';\n\ntype ContextMenuContentElement = React.ComponentRef<typeof MenuPrimitive.Content>;\ntype MenuContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Content>;\ninterface ContextMenuContentProps\n  extends Omit<MenuContentProps, 'onEntryFocus' | 'side' | 'sideOffset' | 'align'> {}\n\nconst ContextMenuContent = React.forwardRef<ContextMenuContentElement, ContextMenuContentProps>(\n  (props: ScopedProps<ContextMenuContentProps>, forwardedRef) => {\n    const { __scopeContextMenu, ...contentProps } = props;\n    const context = useContextMenuContext(CONTENT_NAME, __scopeContextMenu);\n    const menuScope = useMenuScope(__scopeContextMenu);\n    const hasInteractedOutsideRef = React.useRef(false);\n\n    return (\n      <MenuPrimitive.Content\n        {...menuScope}\n        {...contentProps}\n        ref={forwardedRef}\n        side=\"right\"\n        sideOffset={2}\n        align=\"start\"\n        onCloseAutoFocus={(event) => {\n          props.onCloseAutoFocus?.(event);\n\n          if (!event.defaultPrevented && hasInteractedOutsideRef.current) {\n            event.preventDefault();\n          }\n\n          hasInteractedOutsideRef.current = false;\n        }}\n        onInteractOutside={(event) => {\n          props.onInteractOutside?.(event);\n\n          if (!event.defaultPrevented && !context.modal) hasInteractedOutsideRef.current = true;\n        }}\n        style={{\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            '--radix-context-menu-content-transform-origin': 'var(--radix-popper-transform-origin)',\n            '--radix-context-menu-content-available-width': 'var(--radix-popper-available-width)',\n            '--radix-context-menu-content-available-height': 'var(--radix-popper-available-height)',\n            '--radix-context-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n            '--radix-context-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n          },\n        }}\n      />\n    );\n  }\n);\n\nContextMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'ContextMenuGroup';\n\ntype ContextMenuGroupElement = React.ComponentRef<typeof MenuPrimitive.Group>;\ntype MenuGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Group>;\ninterface ContextMenuGroupProps extends MenuGroupProps {}\n\nconst ContextMenuGroup = React.forwardRef<ContextMenuGroupElement, ContextMenuGroupProps>(\n  (props: ScopedProps<ContextMenuGroupProps>, forwardedRef) => {\n    const { __scopeContextMenu, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeContextMenu);\n    return <MenuPrimitive.Group {...menuScope} {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nContextMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'ContextMenuLabel';\n\ntype ContextMenuLabelElement = React.ComponentRef<typeof MenuPrimitive.Label>;\ntype MenuLabelProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Label>;\ninterface ContextMenuLabelProps extends MenuLabelProps {}\n\nconst ContextMenuLabel = React.forwardRef<ContextMenuLabelElement, ContextMenuLabelProps>(\n  (props: ScopedProps<ContextMenuLabelProps>, forwardedRef) => {\n    const { __scopeContextMenu, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeContextMenu);\n    return <MenuPrimitive.Label {...menuScope} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nContextMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'ContextMenuItem';\n\ntype ContextMenuItemElement = React.ComponentRef<typeof MenuPrimitive.Item>;\ntype MenuItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Item>;\ninterface ContextMenuItemProps extends MenuItemProps {}\n\nconst ContextMenuItem = React.forwardRef<ContextMenuItemElement, ContextMenuItemProps>(\n  (props: ScopedProps<ContextMenuItemProps>, forwardedRef) => {\n    const { __scopeContextMenu, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeContextMenu);\n    return <MenuPrimitive.Item {...menuScope} {...itemProps} ref={forwardedRef} />;\n  }\n);\n\nContextMenuItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'ContextMenuCheckboxItem';\n\ntype ContextMenuCheckboxItemElement = React.ComponentRef<typeof MenuPrimitive.CheckboxItem>;\ntype MenuCheckboxItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.CheckboxItem>;\ninterface ContextMenuCheckboxItemProps extends MenuCheckboxItemProps {}\n\nconst ContextMenuCheckboxItem = React.forwardRef<\n  ContextMenuCheckboxItemElement,\n  ContextMenuCheckboxItemProps\n>((props: ScopedProps<ContextMenuCheckboxItemProps>, forwardedRef) => {\n  const { __scopeContextMenu, ...checkboxItemProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return <MenuPrimitive.CheckboxItem {...menuScope} {...checkboxItemProps} ref={forwardedRef} />;\n});\n\nContextMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'ContextMenuRadioGroup';\n\ntype ContextMenuRadioGroupElement = React.ComponentRef<typeof MenuPrimitive.RadioGroup>;\ntype MenuRadioGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioGroup>;\ninterface ContextMenuRadioGroupProps extends MenuRadioGroupProps {}\n\nconst ContextMenuRadioGroup = React.forwardRef<\n  ContextMenuRadioGroupElement,\n  ContextMenuRadioGroupProps\n>((props: ScopedProps<ContextMenuRadioGroupProps>, forwardedRef) => {\n  const { __scopeContextMenu, ...radioGroupProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return <MenuPrimitive.RadioGroup {...menuScope} {...radioGroupProps} ref={forwardedRef} />;\n});\n\nContextMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'ContextMenuRadioItem';\n\ntype ContextMenuRadioItemElement = React.ComponentRef<typeof MenuPrimitive.RadioItem>;\ntype MenuRadioItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioItem>;\ninterface ContextMenuRadioItemProps extends MenuRadioItemProps {}\n\nconst ContextMenuRadioItem = React.forwardRef<\n  ContextMenuRadioItemElement,\n  ContextMenuRadioItemProps\n>((props: ScopedProps<ContextMenuRadioItemProps>, forwardedRef) => {\n  const { __scopeContextMenu, ...radioItemProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return <MenuPrimitive.RadioItem {...menuScope} {...radioItemProps} ref={forwardedRef} />;\n});\n\nContextMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'ContextMenuItemIndicator';\n\ntype ContextMenuItemIndicatorElement = React.ComponentRef<typeof MenuPrimitive.ItemIndicator>;\ntype MenuItemIndicatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.ItemIndicator>;\ninterface ContextMenuItemIndicatorProps extends MenuItemIndicatorProps {}\n\nconst ContextMenuItemIndicator = React.forwardRef<\n  ContextMenuItemIndicatorElement,\n  ContextMenuItemIndicatorProps\n>((props: ScopedProps<ContextMenuItemIndicatorProps>, forwardedRef) => {\n  const { __scopeContextMenu, ...itemIndicatorProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return <MenuPrimitive.ItemIndicator {...menuScope} {...itemIndicatorProps} ref={forwardedRef} />;\n});\n\nContextMenuItemIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'ContextMenuSeparator';\n\ntype ContextMenuSeparatorElement = React.ComponentRef<typeof MenuPrimitive.Separator>;\ntype MenuSeparatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Separator>;\ninterface ContextMenuSeparatorProps extends MenuSeparatorProps {}\n\nconst ContextMenuSeparator = React.forwardRef<\n  ContextMenuSeparatorElement,\n  ContextMenuSeparatorProps\n>((props: ScopedProps<ContextMenuSeparatorProps>, forwardedRef) => {\n  const { __scopeContextMenu, ...separatorProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return <MenuPrimitive.Separator {...menuScope} {...separatorProps} ref={forwardedRef} />;\n});\n\nContextMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'ContextMenuArrow';\n\ntype ContextMenuArrowElement = React.ComponentRef<typeof MenuPrimitive.Arrow>;\ntype MenuArrowProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Arrow>;\ninterface ContextMenuArrowProps extends MenuArrowProps {}\n\nconst ContextMenuArrow = React.forwardRef<ContextMenuArrowElement, ContextMenuArrowProps>(\n  (props: ScopedProps<ContextMenuArrowProps>, forwardedRef) => {\n    const { __scopeContextMenu, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeContextMenu);\n    return <MenuPrimitive.Arrow {...menuScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nContextMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuSub\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_NAME = 'ContextMenuSub';\n\ninterface ContextMenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst ContextMenuSub: React.FC<ContextMenuSubProps> = (props: ScopedProps<ContextMenuSubProps>) => {\n  const { __scopeContextMenu, children, onOpenChange, open: openProp, defaultOpen } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: SUB_NAME,\n  });\n\n  return (\n    <MenuPrimitive.Sub {...menuScope} open={open} onOpenChange={setOpen}>\n      {children}\n    </MenuPrimitive.Sub>\n  );\n};\n\nContextMenuSub.displayName = SUB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'ContextMenuSubTrigger';\n\ntype ContextMenuSubTriggerElement = React.ComponentRef<typeof MenuPrimitive.SubTrigger>;\ntype MenuSubTriggerProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubTrigger>;\ninterface ContextMenuSubTriggerProps extends MenuSubTriggerProps {}\n\nconst ContextMenuSubTrigger = React.forwardRef<\n  ContextMenuSubTriggerElement,\n  ContextMenuSubTriggerProps\n>((props: ScopedProps<ContextMenuSubTriggerProps>, forwardedRef) => {\n  const { __scopeContextMenu, ...triggerItemProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return <MenuPrimitive.SubTrigger {...menuScope} {...triggerItemProps} ref={forwardedRef} />;\n});\n\nContextMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'ContextMenuSubContent';\n\ntype ContextMenuSubContentElement = React.ComponentRef<typeof MenuPrimitive.Content>;\ntype MenuSubContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubContent>;\ninterface ContextMenuSubContentProps extends MenuSubContentProps {}\n\nconst ContextMenuSubContent = React.forwardRef<\n  ContextMenuSubContentElement,\n  ContextMenuSubContentProps\n>((props: ScopedProps<ContextMenuSubContentProps>, forwardedRef) => {\n  const { __scopeContextMenu, ...subContentProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n\n  return (\n    <MenuPrimitive.SubContent\n      {...menuScope}\n      {...subContentProps}\n      ref={forwardedRef}\n      style={{\n        ...props.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-context-menu-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-context-menu-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-context-menu-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-context-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-context-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nContextMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction whenTouchOrPen<E>(handler: React.PointerEventHandler<E>): React.PointerEventHandler<E> {\n  return (event) => (event.pointerType !== 'mouse' ? handler(event) : undefined);\n}\n\nconst Root = ContextMenu;\nconst Trigger = ContextMenuTrigger;\nconst Portal = ContextMenuPortal;\nconst Content = ContextMenuContent;\nconst Group = ContextMenuGroup;\nconst Label = ContextMenuLabel;\nconst Item = ContextMenuItem;\nconst CheckboxItem = ContextMenuCheckboxItem;\nconst RadioGroup = ContextMenuRadioGroup;\nconst RadioItem = ContextMenuRadioItem;\nconst ItemIndicator = ContextMenuItemIndicator;\nconst Separator = ContextMenuSeparator;\nconst Arrow = ContextMenuArrow;\nconst Sub = ContextMenuSub;\nconst SubTrigger = ContextMenuSubTrigger;\nconst SubContent = ContextMenuSubContent;\n\nexport {\n  createContextMenuScope,\n  //\n  ContextMenu,\n  ContextMenuTrigger,\n  ContextMenuPortal,\n  ContextMenuContent,\n  ContextMenuGroup,\n  ContextMenuLabel,\n  ContextMenuItem,\n  ContextMenuCheckboxItem,\n  ContextMenuRadioGroup,\n  ContextMenuRadioItem,\n  ContextMenuItemIndicator,\n  ContextMenuSeparator,\n  ContextMenuArrow,\n  ContextMenuSub,\n  ContextMenuSubTrigger,\n  ContextMenuSubContent,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  ContextMenuProps,\n  ContextMenuTriggerProps,\n  ContextMenuPortalProps,\n  ContextMenuContentProps,\n  ContextMenuGroupProps,\n  ContextMenuLabelProps,\n  ContextMenuItemProps,\n  ContextMenuCheckboxItemProps,\n  ContextMenuRadioGroupProps,\n  ContextMenuRadioItemProps,\n  ContextMenuItemIndicatorProps,\n  ContextMenuSeparatorProps,\n  ContextMenuArrowProps,\n  ContextMenuSubProps,\n  ContextMenuSubTriggerProps,\n  ContextMenuSubContentProps,\n};\n"], "mappings": ";;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,0BAA0B;AACnC,SAAS,iBAAiB;AAC1B,YAAY,mBAAmB;AAC/B,SAAS,uBAAuB;AAChC,SAAS,sBAAsB;AAC/B,SAAS,4BAA4B;AAwD/B,SAkDA,UAlDA,KAkDA,YAlDA;AA7CN,IAAM,oBAAoB;AAG1B,IAAM,CAAC,0BAA0B,sBAAsB,IAAI,mBAAmB,mBAAmB;AAAA,EAC/F;AACF,CAAC;AACD,IAAM,eAAe,gBAAgB;AAQrC,IAAM,CAAC,qBAAqB,qBAAqB,IAC/C,yBAAkD,iBAAiB;AASrE,IAAM,cAA0C,CAAC,UAAyC;AACxF,QAAM,EAAE,oBAAoB,UAAU,cAAc,KAAK,QAAQ,KAAK,IAAI;AAC1E,QAAM,CAAC,MAAM,OAAO,IAAU,eAAS,KAAK;AAC5C,QAAM,YAAY,aAAa,kBAAkB;AACjD,QAAM,uBAAuB,eAAe,YAAY;AAExD,QAAM,mBAAyB;AAAA,IAC7B,CAACA,UAAkB;AACjB,cAAQA,KAAI;AACZ,2BAAqBA,KAAI;AAAA,IAC3B;AAAA,IACA,CAAC,oBAAoB;AAAA,EACvB;AAEA,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO;AAAA,MACP;AAAA,MACA,cAAc;AAAA,MACd;AAAA,MAEA;AAAA,QAAe;AAAA,QAAd;AAAA,UACE,GAAG;AAAA,UACJ;AAAA,UACA;AAAA,UACA,cAAc;AAAA,UACd;AAAA,UAEC;AAAA;AAAA,MACH;AAAA;AAAA,EACF;AAEJ;AAEA,YAAY,cAAc;AAM1B,IAAM,eAAe;AAQrB,IAAM,qBAA2B;AAAA,EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,oBAAoB,WAAW,OAAO,GAAG,aAAa,IAAI;AAClE,UAAM,UAAU,sBAAsB,cAAc,kBAAkB;AACtE,UAAM,YAAY,aAAa,kBAAkB;AACjD,UAAM,WAAiB,aAAc,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AACnD,UAAM,aAAmB,aAAO;AAAA,MAC9B,uBAAuB,MAAM,QAAQ,SAAS,EAAE,OAAO,GAAG,QAAQ,GAAG,GAAG,SAAS,QAAQ,CAAC;AAAA,IAC5F,CAAC;AACD,UAAM,oBAA0B,aAAO,CAAC;AACxC,UAAM,iBAAuB;AAAA,MAC3B,MAAM,OAAO,aAAa,kBAAkB,OAAO;AAAA,MACnD,CAAC;AAAA,IACH;AACA,UAAM,aAAa,CAAC,UAAiD;AACnE,eAAS,UAAU,EAAE,GAAG,MAAM,SAAS,GAAG,MAAM,QAAQ;AACxD,cAAQ,aAAa,IAAI;AAAA,IAC3B;AAEA,IAAM,gBAAU,MAAM,gBAAgB,CAAC,cAAc,CAAC;AACtD,IAAM,gBAAU,MAAM,MAAM,YAAY,eAAe,IAAI,CAAC,UAAU,cAAc,CAAC;AAErF,WACE,iCACE;AAAA,0BAAe,sBAAd,EAAsB,GAAG,WAAW,YAAwB;AAAA,MAC7D;AAAA,QAAC,UAAU;AAAA,QAAV;AAAA,UACC,cAAY,QAAQ,OAAO,SAAS;AAAA,UACpC,iBAAe,WAAW,KAAK;AAAA,UAC9B,GAAG;AAAA,UACJ,KAAK;AAAA,UAEL,OAAO,EAAE,oBAAoB,QAAQ,GAAG,MAAM,MAAM;AAAA,UAEpD,eACE,WACI,MAAM,gBACN,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAGnD,2BAAe;AACf,uBAAW,KAAK;AAChB,kBAAM,eAAe;AAAA,UACvB,CAAC;AAAA,UAEP,eACE,WACI,MAAM,gBACN;AAAA,YACE,MAAM;AAAA,YACN,eAAe,CAAC,UAAU;AAExB,6BAAe;AACf,gCAAkB,UAAU,OAAO,WAAW,MAAM,WAAW,KAAK,GAAG,GAAG;AAAA,YAC5E,CAAC;AAAA,UACH;AAAA,UAEN,eACE,WACI,MAAM,gBACN,qBAAqB,MAAM,eAAe,eAAe,cAAc,CAAC;AAAA,UAE9E,iBACE,WACI,MAAM,kBACN,qBAAqB,MAAM,iBAAiB,eAAe,cAAc,CAAC;AAAA,UAEhF,aACE,WACI,MAAM,cACN,qBAAqB,MAAM,aAAa,eAAe,cAAc,CAAC;AAAA;AAAA,MAE9E;AAAA,OACF;AAAA,EAEJ;AACF;AAEA,mBAAmB,cAAc;AAMjC,IAAM,cAAc;AAKpB,IAAM,oBAAsD,CAC1D,UACG;AACH,QAAM,EAAE,oBAAoB,GAAG,YAAY,IAAI;AAC/C,QAAM,YAAY,aAAa,kBAAkB;AACjD,SAAO,oBAAe,sBAAd,EAAsB,GAAG,WAAY,GAAG,aAAa;AAC/D;AAEA,kBAAkB,cAAc;AAMhC,IAAM,eAAe;AAOrB,IAAM,qBAA2B;AAAA,EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,oBAAoB,GAAG,aAAa,IAAI;AAChD,UAAM,UAAU,sBAAsB,cAAc,kBAAkB;AACtE,UAAM,YAAY,aAAa,kBAAkB;AACjD,UAAM,0BAAgC,aAAO,KAAK;AAElD,WACE;AAAA,MAAe;AAAA,MAAd;AAAA,QACE,GAAG;AAAA,QACH,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,MAAK;AAAA,QACL,YAAY;AAAA,QACZ,OAAM;AAAA,QACN,kBAAkB,CAAC,UAAU;AAC3B,gBAAM,mBAAmB,KAAK;AAE9B,cAAI,CAAC,MAAM,oBAAoB,wBAAwB,SAAS;AAC9D,kBAAM,eAAe;AAAA,UACvB;AAEA,kCAAwB,UAAU;AAAA,QACpC;AAAA,QACA,mBAAmB,CAAC,UAAU;AAC5B,gBAAM,oBAAoB,KAAK;AAE/B,cAAI,CAAC,MAAM,oBAAoB,CAAC,QAAQ,MAAO,yBAAwB,UAAU;AAAA,QACnF;AAAA,QACA,OAAO;AAAA,UACL,GAAG,MAAM;AAAA;AAAA,UAET,GAAG;AAAA,YACD,iDAAiD;AAAA,YACjD,gDAAgD;AAAA,YAChD,iDAAiD;AAAA,YACjD,sCAAsC;AAAA,YACtC,uCAAuC;AAAA,UACzC;AAAA,QACF;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,mBAAmB,cAAc;AAMjC,IAAM,aAAa;AAMnB,IAAM,mBAAyB;AAAA,EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,oBAAoB,GAAG,WAAW,IAAI;AAC9C,UAAM,YAAY,aAAa,kBAAkB;AACjD,WAAO,oBAAe,qBAAd,EAAqB,GAAG,WAAY,GAAG,YAAY,KAAK,cAAc;AAAA,EAChF;AACF;AAEA,iBAAiB,cAAc;AAM/B,IAAM,aAAa;AAMnB,IAAM,mBAAyB;AAAA,EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,oBAAoB,GAAG,WAAW,IAAI;AAC9C,UAAM,YAAY,aAAa,kBAAkB;AACjD,WAAO,oBAAe,qBAAd,EAAqB,GAAG,WAAY,GAAG,YAAY,KAAK,cAAc;AAAA,EAChF;AACF;AAEA,iBAAiB,cAAc;AAM/B,IAAM,YAAY;AAMlB,IAAM,kBAAwB;AAAA,EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM,EAAE,oBAAoB,GAAG,UAAU,IAAI;AAC7C,UAAM,YAAY,aAAa,kBAAkB;AACjD,WAAO,oBAAe,oBAAd,EAAoB,GAAG,WAAY,GAAG,WAAW,KAAK,cAAc;AAAA,EAC9E;AACF;AAEA,gBAAgB,cAAc;AAM9B,IAAM,qBAAqB;AAM3B,IAAM,0BAAgC,iBAGpC,CAAC,OAAkD,iBAAiB;AACpE,QAAM,EAAE,oBAAoB,GAAG,kBAAkB,IAAI;AACrD,QAAM,YAAY,aAAa,kBAAkB;AACjD,SAAO,oBAAe,4BAAd,EAA4B,GAAG,WAAY,GAAG,mBAAmB,KAAK,cAAc;AAC9F,CAAC;AAED,wBAAwB,cAAc;AAMtC,IAAM,mBAAmB;AAMzB,IAAM,wBAA8B,iBAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM,EAAE,oBAAoB,GAAG,gBAAgB,IAAI;AACnD,QAAM,YAAY,aAAa,kBAAkB;AACjD,SAAO,oBAAe,0BAAd,EAA0B,GAAG,WAAY,GAAG,iBAAiB,KAAK,cAAc;AAC1F,CAAC;AAED,sBAAsB,cAAc;AAMpC,IAAM,kBAAkB;AAMxB,IAAM,uBAA6B,iBAGjC,CAAC,OAA+C,iBAAiB;AACjE,QAAM,EAAE,oBAAoB,GAAG,eAAe,IAAI;AAClD,QAAM,YAAY,aAAa,kBAAkB;AACjD,SAAO,oBAAe,yBAAd,EAAyB,GAAG,WAAY,GAAG,gBAAgB,KAAK,cAAc;AACxF,CAAC;AAED,qBAAqB,cAAc;AAMnC,IAAM,iBAAiB;AAMvB,IAAM,2BAAiC,iBAGrC,CAAC,OAAmD,iBAAiB;AACrE,QAAM,EAAE,oBAAoB,GAAG,mBAAmB,IAAI;AACtD,QAAM,YAAY,aAAa,kBAAkB;AACjD,SAAO,oBAAe,6BAAd,EAA6B,GAAG,WAAY,GAAG,oBAAoB,KAAK,cAAc;AAChG,CAAC;AAED,yBAAyB,cAAc;AAMvC,IAAM,iBAAiB;AAMvB,IAAM,uBAA6B,iBAGjC,CAAC,OAA+C,iBAAiB;AACjE,QAAM,EAAE,oBAAoB,GAAG,eAAe,IAAI;AAClD,QAAM,YAAY,aAAa,kBAAkB;AACjD,SAAO,oBAAe,yBAAd,EAAyB,GAAG,WAAY,GAAG,gBAAgB,KAAK,cAAc;AACxF,CAAC;AAED,qBAAqB,cAAc;AAMnC,IAAM,aAAa;AAMnB,IAAM,mBAAyB;AAAA,EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,oBAAoB,GAAG,WAAW,IAAI;AAC9C,UAAM,YAAY,aAAa,kBAAkB;AACjD,WAAO,oBAAe,qBAAd,EAAqB,GAAG,WAAY,GAAG,YAAY,KAAK,cAAc;AAAA,EAChF;AACF;AAEA,iBAAiB,cAAc;AAM/B,IAAM,WAAW;AASjB,IAAM,iBAAgD,CAAC,UAA4C;AACjG,QAAM,EAAE,oBAAoB,UAAU,cAAc,MAAM,UAAU,YAAY,IAAI;AACpF,QAAM,YAAY,aAAa,kBAAkB;AACjD,QAAM,CAAC,MAAM,OAAO,IAAI,qBAAqB;AAAA,IAC3C,MAAM;AAAA,IACN,aAAa,eAAe;AAAA,IAC5B,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AAED,SACE,oBAAe,mBAAd,EAAmB,GAAG,WAAW,MAAY,cAAc,SACzD,UACH;AAEJ;AAEA,eAAe,cAAc;AAM7B,IAAM,mBAAmB;AAMzB,IAAM,wBAA8B,iBAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM,EAAE,oBAAoB,GAAG,iBAAiB,IAAI;AACpD,QAAM,YAAY,aAAa,kBAAkB;AACjD,SAAO,oBAAe,0BAAd,EAA0B,GAAG,WAAY,GAAG,kBAAkB,KAAK,cAAc;AAC3F,CAAC;AAED,sBAAsB,cAAc;AAMpC,IAAM,mBAAmB;AAMzB,IAAM,wBAA8B,iBAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM,EAAE,oBAAoB,GAAG,gBAAgB,IAAI;AACnD,QAAM,YAAY,aAAa,kBAAkB;AAEjD,SACE;AAAA,IAAe;AAAA,IAAd;AAAA,MACE,GAAG;AAAA,MACH,GAAG;AAAA,MACJ,KAAK;AAAA,MACL,OAAO;AAAA,QACL,GAAG,MAAM;AAAA;AAAA,QAET,GAAG;AAAA,UACD,iDAAiD;AAAA,UACjD,gDAAgD;AAAA,UAChD,iDAAiD;AAAA,UACjD,sCAAsC;AAAA,UACtC,uCAAuC;AAAA,QACzC;AAAA,MACF;AAAA;AAAA,EACF;AAEJ,CAAC;AAED,sBAAsB,cAAc;AAIpC,SAAS,eAAkB,SAAqE;AAC9F,SAAO,CAAC,UAAW,MAAM,gBAAgB,UAAU,QAAQ,KAAK,IAAI;AACtE;AAEA,IAAMC,QAAO;AACb,IAAM,UAAU;AAChB,IAAMC,UAAS;AACf,IAAMC,WAAU;AAChB,IAAMC,SAAQ;AACd,IAAMC,SAAQ;AACd,IAAMC,QAAO;AACb,IAAMC,gBAAe;AACrB,IAAMC,cAAa;AACnB,IAAMC,aAAY;AAClB,IAAMC,iBAAgB;AACtB,IAAMC,aAAY;AAClB,IAAMC,SAAQ;AACd,IAAMC,OAAM;AACZ,IAAMC,cAAa;AACnB,IAAMC,cAAa;", "names": ["open", "Root", "Portal", "Content", "Group", "Label", "<PERSON><PERSON>", "CheckboxItem", "RadioGroup", "RadioItem", "ItemIndicator", "Separator", "Arrow", "Sub", "SubTrigger", "SubContent"]}