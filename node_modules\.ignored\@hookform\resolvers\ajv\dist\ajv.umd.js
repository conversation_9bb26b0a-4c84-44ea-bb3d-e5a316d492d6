!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("@hookform/resolvers"),require("ajv"),require("ajv-errors"),require("react-hook-form")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers","ajv","ajv-errors","react-hook-form"],r):r((e||self).hookformResolversAjv={},e.hookformResolvers,e.ajv,e.ajvErrors,e.ReactHookForm)}(this,function(e,r,o,a,s){function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var i=/*#__PURE__*/t(o),n=/*#__PURE__*/t(a),u=function(e,r){return e.forEach(function(e){"required"===e.keyword&&(e.instancePath+="/"+e.params.missingProperty)}),e.reduce(function(e,o){var a=o.instancePath.substring(1).replace(/\//g,".");if(e[a]||(e[a]={message:o.message,type:o.keyword}),r){var t=e[a].types,i=t&&t[o.keyword];e[a]=s.appendErrors(a,r,e,o.keyword,i?[].concat(i,o.message||""):o.message)}return e},{})};e.ajvResolver=function(e,o,a){return void 0===a&&(a={}),function(s,t,f){try{var l=new i.default(Object.assign({},{allErrors:!0,validateSchema:!0},o));n.default(l);var c=l.compile(Object.assign({$async:a&&"async"===a.mode},e)),d=c(s);return f.shouldUseNativeValidation&&r.validateFieldsNatively({},f),Promise.resolve(d?{values:s,errors:{}}:{values:{},errors:r.toNestErrors(u(c.errors,!f.shouldUseNativeValidation&&"all"===f.criteriaMode),f)})}catch(e){return Promise.reject(e)}}}});
//# sourceMappingURL=ajv.umd.js.map
