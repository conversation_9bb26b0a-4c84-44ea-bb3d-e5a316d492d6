{"version": 3, "file": "install.js", "sourceRoot": "", "sources": ["../../src/install.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;AAuIH,0BA0EC;AA2MD,8BAaC;AAiBD,oDAIC;AAKD,kCAeC;AASD,wCAOC;AAKD,oDAyBC;AA9fD,8DAAiC;AACjC,2DAA6C;AAC7C,qCAAiD;AACjD,+CAA+C;AAC/C,sDAAyB;AACzB,0DAA6B;AAG7B,wDAAwC;AAExC,oEAIwC;AACxC,yCAAmD;AACnD,yCAAiC;AACjC,2DAA0D;AAC1D,+CAA4C;AAC5C,+CAAqE;AAErE,MAAM,YAAY,GAAG,IAAA,gBAAK,EAAC,4BAA4B,CAAC,CAAC;AAEzD,MAAM,KAAK,GAAG,IAAI,GAAG,EAA4B,CAAC;AAClD,SAAS,SAAS,CAAC,KAAa;IAC9B,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;AACrC,CAAC;AAED,SAAS,YAAY,CAAC,KAAa;IACjC,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAC7B,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO;IACT,CAAC;IACD,MAAM,QAAQ,GACZ,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,qCAAqC;IAC1G,YAAY,CAAC,gBAAgB,KAAK,KAAK,QAAQ,IAAI,CAAC,CAAC;AACvD,CAAC;AAgGM,KAAK,UAAU,OAAO,CAC3B,OAAuB;IAEvB,OAAO,CAAC,QAAQ,KAAK,IAAA,yCAAqB,GAAE,CAAC;IAC7C,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC;IACxB,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QACtB,MAAM,IAAI,KAAK,CACb,uDAAuD,iBAAE,CAAC,QAAQ,EAAE,KAAK,iBAAE,CAAC,IAAI,EAAE,GAAG,CACtF,CAAC;IACJ,CAAC;IACD,MAAM,GAAG,GAAG,cAAc,CACxB,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,OAAO,CAChB,CAAC;IACF,IAAI,CAAC;QACH,OAAO,MAAM,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,oEAAoE;QACpE,IAAI,OAAO,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,CAAC;YACxD,MAAM,GAAG,CAAC;QACZ,CAAC;QACD,YAAY,CAAC,0BAA0B,GAAG,GAAG,CAAC,CAAC;QAC/C,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC;YACxB,KAAK,yBAAO,CAAC,MAAM,CAAC;YACpB,KAAK,yBAAO,CAAC,YAAY,CAAC;YAC1B,KAAK,yBAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC;gBACjC,YAAY,CACV,wFAAwF,CACzF,CAAC;gBAIF,MAAM,OAAO,GAAG,CAAC,MAAM,IAAA,qBAAO,EAC5B,IAAI,GAAG,CACL,yDAAyD,OAAO,CAAC,OAAO,OAAO,CAChF,CACF,CAAY,CAAC;gBACd,IAAI,QAAQ,GAAG,EAAE,CAAC;gBAClB,QAAQ,OAAO,CAAC,QAAQ,EAAE,CAAC;oBACzB,KAAK,iCAAe,CAAC,KAAK;wBACxB,QAAQ,GAAG,SAAS,CAAC;wBACrB,MAAM;oBACR,KAAK,iCAAe,CAAC,OAAO;wBAC1B,QAAQ,GAAG,WAAW,CAAC;wBACvB,MAAM;oBACR,KAAK,iCAAe,CAAC,GAAG;wBACtB,QAAQ,GAAG,SAAS,CAAC;wBACrB,MAAM;oBACR,KAAK,iCAAe,CAAC,KAAK;wBACxB,QAAQ,GAAG,OAAO,CAAC;wBACnB,MAAM;oBACR,KAAK,iCAAe,CAAC,KAAK;wBACxB,QAAQ,GAAG,OAAO,CAAC;wBACnB,MAAM;gBACV,CAAC;gBACD,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;oBAChE,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,QAAQ,CAAC;gBACvC,CAAC,CAAC,EAAE,GAAG,CAAC;gBACR,IAAI,SAAS,EAAE,CAAC;oBACd,0CAA0C;oBAC1C,IAAI,SAAS,KAAK,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC;wBACjC,MAAM,GAAG,CAAC;oBACZ,CAAC;oBACD,YAAY,CAAC,oCAAoC,SAAS,GAAG,CAAC,CAAC;oBAC/D,OAAO,MAAM,UAAU,CAAC,IAAI,GAAG,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;gBACvD,CAAC;gBACD,MAAM,GAAG,CAAC;YACZ,CAAC;YACD;gBACE,MAAM,GAAG,CAAC;QACd,CAAC;IACH,CAAC;AACH,CAAC;AAED,KAAK,UAAU,WAAW,CAAC,gBAAkC;IAC3D,IACE,OAAO,CAAC,QAAQ,KAAK,OAAO;QAC5B,gBAAgB,CAAC,QAAQ,KAAK,iCAAe,CAAC,KAAK,EACnD,CAAC;QACD,OAAO;IACT,CAAC;IACD,kDAAkD;IAClD,MAAM,QAAQ,GAAG,mBAAI,CAAC,IAAI,CACxB,mBAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAC7C,UAAU,CACX,CAAC;IACF,IAAI,CAAC,IAAA,oBAAU,EAAC,QAAQ,CAAC,EAAE,CAAC;QAC1B,YAAY,CAAC,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC3D,OAAO;IACT,CAAC;IACD,MAAM,IAAI,GAAG,IAAA,sBAAY,EAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACnE,IAAI,OAAO,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC;QAC7B,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;IAC7E,CAAC;IACD,IAAI,MAAM,GAAG,IAAA,8BAAS,EAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1C,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACxB,MAAM,IAAI,KAAK,CACb,8EAA8E,CAC/E,CAAC;IACJ,CAAC;IACD,YAAY,CAAC,mCAAmC,IAAI,EAAE,CAAC,CAAC;IACxD,MAAM,GAAG,IAAA,8BAAS,EAAC,SAAS,EAAE;QAC5B,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,yBAAyB;KAC1B,CAAC,CAAC;IACH,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACxB,MAAM,IAAI,KAAK,CACb,iDAAiD,MAAM,CAAC,MAAM,UAAU,MAAM,CAAC,KAAK,WAAW,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CACzK,CAAC;IACJ,CAAC;IACD,YAAY,CAAC,iCAAiC,IAAI,EAAE,CAAC,CAAC;AACxD,CAAC;AAED,KAAK,UAAU,UAAU,CACvB,GAAQ,EACR,OAAuB;IAEvB,OAAO,CAAC,QAAQ,KAAK,IAAA,yCAAqB,GAAE,CAAC;IAC7C,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QACtB,MAAM,IAAI,KAAK,CACb,uDAAuD,iBAAE,CAAC,QAAQ,EAAE,KAAK,iBAAE,CAAC,IAAI,EAAE,GAAG,CACtF,CAAC;IACJ,CAAC;IACD,IAAI,wBAAwB,GAAG,OAAO,CAAC,wBAAwB,CAAC;IAChE,IAAI,wBAAwB,KAAK,SAAS,EAAE,CAAC;QAC3C,wBAAwB,GAAG,MAAM,oBAAoB,CACnD,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,OAAO,CACxC,CAAC;IACJ,CAAC;IACD,MAAM,QAAQ,GAAG,kBAAkB,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;IACrE,IAAA,qBAAM,EAAC,QAAQ,EAAE,uCAAuC,GAAG,GAAG,CAAC,CAAC;IAChE,MAAM,KAAK,GAAG,IAAI,gBAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC1C,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACvD,MAAM,WAAW,GAAG,mBAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,OAAO,IAAI,QAAQ,EAAE,CAAC,CAAC;IAC7E,IAAI,CAAC,IAAA,oBAAU,EAAC,WAAW,CAAC,EAAE,CAAC;QAC7B,MAAM,IAAA,gBAAK,EAAC,WAAW,EAAE,EAAC,SAAS,EAAE,IAAI,EAAC,CAAC,CAAC;IAC9C,CAAC;IAED,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QACpB,IAAI,IAAA,oBAAU,EAAC,WAAW,CAAC,EAAE,CAAC;YAC5B,OAAO,WAAW,CAAC;QACrB,CAAC;QACD,YAAY,CAAC,2BAA2B,GAAG,EAAE,CAAC,CAAC;QAC/C,SAAS,CAAC,UAAU,CAAC,CAAC;QACtB,MAAM,IAAA,0BAAY,EAAC,GAAG,EAAE,WAAW,EAAE,wBAAwB,CAAC,CAAC;QAC/D,YAAY,CAAC,UAAU,CAAC,CAAC;QACzB,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,MAAM,UAAU,GAAG,KAAK,CAAC,eAAe,CACtC,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,OAAO,CAChB,CAAC;IAEF,IAAI,CAAC;QACH,IAAI,IAAA,oBAAU,EAAC,UAAU,CAAC,EAAE,CAAC;YAC3B,MAAM,gBAAgB,GAAG,IAAI,2BAAgB,CAC3C,KAAK,EACL,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,QAAQ,CACjB,CAAC;YACF,IAAI,CAAC,IAAA,oBAAU,EAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC;gBACjD,MAAM,IAAI,KAAK,CACb,uBAAuB,UAAU,gCAAgC,gBAAgB,CAAC,cAAc,cAAc,CAC/G,CAAC;YACJ,CAAC;YACD,MAAM,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YACjC,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACxB,MAAM,WAAW,CAAC,gBAAgB,CAAC,CAAC;YACtC,CAAC;YACD,OAAO,gBAAgB,CAAC;QAC1B,CAAC;QACD,YAAY,CAAC,2BAA2B,GAAG,EAAE,CAAC,CAAC;QAC/C,IAAI,CAAC;YACH,SAAS,CAAC,UAAU,CAAC,CAAC;YACtB,MAAM,IAAA,0BAAY,EAAC,GAAG,EAAE,WAAW,EAAE,wBAAwB,CAAC,CAAC;QACjE,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC;QAED,YAAY,CAAC,cAAc,WAAW,OAAO,UAAU,EAAE,CAAC,CAAC;QAC3D,IAAI,CAAC;YACH,SAAS,CAAC,SAAS,CAAC,CAAC;YACrB,MAAM,IAAA,2BAAa,EAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QAC/C,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,SAAS,CAAC,CAAC;QAC1B,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,2BAAgB,CAC3C,KAAK,EACL,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,QAAQ,CACjB,CAAC;QACF,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACzB,MAAM,QAAQ,GAAG,gBAAgB,CAAC,YAAY,EAAE,CAAC;YACjD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;YACzD,gBAAgB,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC3C,CAAC;QAED,MAAM,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QACjC,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,MAAM,WAAW,CAAC,gBAAgB,CAAC,CAAC;QACtC,CAAC;QACD,OAAO,gBAAgB,CAAC;IAC1B,CAAC;YAAS,CAAC;QACT,IAAI,IAAA,oBAAU,EAAC,WAAW,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAA,iBAAM,EAAC,WAAW,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;AACH,CAAC;AAED,KAAK,UAAU,QAAQ,CAAC,gBAAkC;IACxD,iEAAiE;IACjE,IACE,CAAC,gBAAgB,CAAC,QAAQ,KAAK,iCAAe,CAAC,KAAK;QAClD,gBAAgB,CAAC,QAAQ,KAAK,iCAAe,CAAC,KAAK,CAAC;QACtD,gBAAgB,CAAC,OAAO,KAAK,yBAAO,CAAC,MAAM;QAC3C,gBAAgB,CAAC,QAAQ,KAAK,IAAA,yCAAqB,GAAE,EACrD,CAAC;QACD,IAAI,CAAC;YACH,SAAS,CAAC,aAAa,CAAC,CAAC;YACzB,MAAM,UAAU,GAAG,mBAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;YACjE,MAAM,YAAY,GAAG,mBAAI,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YACxD,IAAI,CAAC,IAAA,oBAAU,EAAC,YAAY,CAAC,EAAE,CAAC;gBAC9B,OAAO;YACT,CAAC;YACD,IAAA,8BAAS,EACP,mBAAI,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,EAClC,CAAC,mCAAmC,GAAG,UAAU,CAAC,EAClD;gBACE,KAAK,EAAE,IAAI;aACZ,CACF,CAAC;YACF,6DAA6D;YAC7D,qDAAqD;QACvD,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,aAAa,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;AACH,CAAC;AA0BD;;;GAGG;AACI,KAAK,UAAU,SAAS,CAAC,OAAyB;IACvD,OAAO,CAAC,QAAQ,KAAK,IAAA,yCAAqB,GAAE,CAAC;IAC7C,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QACtB,MAAM,IAAI,KAAK,CACb,2CAA2C,iBAAE,CAAC,QAAQ,EAAE,KAAK,iBAAE,CAAC,IAAI,EAAE,GAAG,CAC1E,CAAC;IACJ,CAAC;IAED,IAAI,gBAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,SAAS,CACnC,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,OAAO,CAChB,CAAC;AACJ,CAAC;AAYD;;;;GAIG;AACI,KAAK,UAAU,oBAAoB,CACxC,OAAoC;IAEpC,OAAO,IAAI,gBAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,EAAE,CAAC;AAC5D,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,WAAW,CAAC,OAAuB;IACvD,OAAO,CAAC,QAAQ,KAAK,IAAA,yCAAqB,GAAE,CAAC;IAC7C,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QACtB,MAAM,IAAI,KAAK,CACb,uDAAuD,iBAAE,CAAC,QAAQ,EAAE,KAAK,iBAAE,CAAC,IAAI,EAAE,GAAG,CACtF,CAAC;IACJ,CAAC;IACD,OAAO,MAAM,IAAA,6BAAe,EAC1B,cAAc,CACZ,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,OAAO,CAChB,CACF,CAAC;AACJ,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,cAAc,CAC5B,OAAgB,EAChB,QAAyB,EACzB,OAAe,EACf,OAAgB;IAEhB,OAAO,IAAI,GAAG,CAAC,8BAAY,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;AACpE,CAAC;AAED;;GAEG;AACH,SAAgB,oBAAoB,CAClC,OAAgB,EAChB,OAAe;IAEf,IAAI,WAAwB,CAAC;IAE7B,IAAI,mBAAmB,GAAG,CAAC,CAAC;IAC5B,OAAO,CAAC,eAAuB,EAAE,UAAkB,EAAE,EAAE;QACrD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,WAAW,GAAG,IAAI,kBAAgB,CAChC,eAAe,OAAO,IAAI,OAAO,MAAM,WAAW,CAChD,UAAU,CACX,yBAAyB,EAC1B;gBACE,QAAQ,EAAE,GAAG;gBACb,UAAU,EAAE,GAAG;gBACf,KAAK,EAAE,EAAE;gBACT,KAAK,EAAE,UAAU;aAClB,CACF,CAAC;QACJ,CAAC;QACD,MAAM,KAAK,GAAG,eAAe,GAAG,mBAAmB,CAAC;QACpD,mBAAmB,GAAG,eAAe,CAAC;QACtC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,KAAa;IAChC,MAAM,EAAE,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC;IAC/B,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC;AAC1C,CAAC"}