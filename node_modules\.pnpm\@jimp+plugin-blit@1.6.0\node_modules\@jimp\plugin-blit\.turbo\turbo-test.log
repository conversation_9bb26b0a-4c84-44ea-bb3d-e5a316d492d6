

> @jimp/plugin-blit@1.1.2 test /Users/<USER>/Documents/jimp/plugins/plugin-blit
> vitest "--watch=false"


[7m[1m[36m RUN [39m[22m[27m [36mv2.0.5[39m [90m/Users/<USER>/Documents/jimp/plugins/plugin-blit[39m

[?25l [90m·[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[?25l[?25l[?25l[2K[1A[2K[G [90m·[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[?25l[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠙[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠹[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠸[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠼[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠴[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠦[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠧[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠇[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠏[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠋[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠙[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠹[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠸[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠼[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠴[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠦[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠧[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠇[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠏[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠋[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠙[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠹[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠸[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠼[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠴[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠦[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠧[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠇[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠏[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠋[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠙[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠹[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠸[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠼[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠴[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠦[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠧[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠇[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠏[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠋[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠙[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠹[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠸[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠼[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠴[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠦[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠧[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠇[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠏[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠋[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠙[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠹[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠸[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠼[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠴[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠦[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠧[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠇[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠏[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠋[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠙[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠹[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠸[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠼[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠴[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠦[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠧[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠇[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠏[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠋[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠙[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠹[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠸[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠼[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠴[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠦[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠧[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠇[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠏[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠋[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠙[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠹[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠸[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠼[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠴[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠦[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠧[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠇[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠏[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠋[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠙[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠹[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠸[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠼[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠴[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠦[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠧[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠇[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠏[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠋[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠙[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠹[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠸[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠼[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠴[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠦[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠧[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠇[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠏[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠋[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠙[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠹[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠸[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [33m⠼[39m blit alpha
     [90m·[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠙[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠹[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠸[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠼[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠴[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠦[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠧[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠇[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠏[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠋[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠙[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠹[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠸[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠼[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠴[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠦[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠧[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠇[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠏[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠋[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠙[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠹[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠸[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠼[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠴[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠦[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠧[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠇[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠏[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠋[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠙[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠹[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠸[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠼[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠴[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠦[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠧[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠇[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠏[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠋[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠙[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠹[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠸[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠼[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠴[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠦[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠧[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠇[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠏[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠋[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠙[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠹[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠸[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠼[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠴[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠦[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠧[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠇[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠏[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠋[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠙[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠹[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠸[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠼[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠴[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠦[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠧[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠇[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠏[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠋[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠙[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠹[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠸[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠼[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠴[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠦[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠧[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠇[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠏[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠋[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠙[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠹[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠸[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠼[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠴[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠦[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠧[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠇[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠏[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠋[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠙[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠹[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠸[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠼[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠴[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠦[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠧[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠇[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠏[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠋[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠙[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠹[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠸[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠼[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠴[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠦[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠧[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠇[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠏[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠋[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠙[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠹[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠸[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠼[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠴[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠦[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠧[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Blit over image[2m (2)[22m
     [32m✓[39m blit alpha[33m 2424[2mms[22m[39m
     [33m⠇[39m uses src params correctly
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m[33m 5093[2mms[22m[39m
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (2)[22m[33m 5093[2mms[22m[39m
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m

[2m Test Files [22m [1m[32m2 passed[39m[22m[90m (2)[39m
[2m      Tests [22m [1m[32m8 passed[39m[22m[90m (8)[39m
[2m   Start at [22m 01:33:59
[2m   Duration [22m 7.03s[2m (transform 446ms, setup 0ms, collect 1.90s, tests 5.25s, environment 0ms, prepare 340ms)[22m

[?25h[?25h
