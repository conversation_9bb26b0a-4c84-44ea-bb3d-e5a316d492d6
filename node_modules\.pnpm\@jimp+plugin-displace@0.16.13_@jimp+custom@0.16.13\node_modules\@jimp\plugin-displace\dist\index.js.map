{"version": 3, "sources": ["../src/index.js"], "names": ["displace", "map", "offset", "cb", "constructor", "throwError", "call", "source", "clone<PERSON>uiet", "scanQuiet", "bitmap", "width", "height", "x", "y", "idx", "displacement", "data", "Math", "round", "ids", "getPixelIndex"], "mappings": ";;;;;;;;;;;AAAA;;AAEA;;;;;;;eAOe;AAAA,SAAO;AACpBA,IAAAA,QADoB,oBACXC,GADW,EACNC,MADM,EACEC,EADF,EACM;AACxB,UAAI,yBAAOF,GAAP,MAAe,QAAf,IAA2BA,GAAG,CAACG,WAAJ,KAAoB,KAAKA,WAAxD,EAAqE;AACnE,eAAOC,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,iCAAtB,EAAyDH,EAAzD,CAAP;AACD;;AAED,UAAI,OAAOD,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,eAAOG,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,yBAAtB,EAAiDH,EAAjD,CAAP;AACD;;AAED,UAAMI,MAAM,GAAG,KAAKC,UAAL,EAAf;AACA,WAAKC,SAAL,CACE,CADF,EAEE,CAFF,EAGE,KAAKC,MAAL,CAAYC,KAHd,EAIE,KAAKD,MAAL,CAAYE,MAJd,EAKE,UAAUC,CAAV,EAAaC,CAAb,EAAgBC,GAAhB,EAAqB;AACnB,YAAIC,YAAY,GAAIf,GAAG,CAACS,MAAJ,CAAWO,IAAX,CAAgBF,GAAhB,IAAuB,GAAxB,GAA+Bb,MAAlD;AACAc,QAAAA,YAAY,GAAGE,IAAI,CAACC,KAAL,CAAWH,YAAX,CAAf;AAEA,YAAMI,GAAG,GAAG,KAAKC,aAAL,CAAmBR,CAAC,GAAGG,YAAvB,EAAqCF,CAArC,CAAZ;AACA,aAAKJ,MAAL,CAAYO,IAAZ,CAAiBG,GAAjB,IAAwBb,MAAM,CAACG,MAAP,CAAcO,IAAd,CAAmBF,GAAnB,CAAxB;AACA,aAAKL,MAAL,CAAYO,IAAZ,CAAiBG,GAAG,GAAG,CAAvB,IAA4Bb,MAAM,CAACG,MAAP,CAAcO,IAAd,CAAmBF,GAAG,GAAG,CAAzB,CAA5B;AACA,aAAKL,MAAL,CAAYO,IAAZ,CAAiBG,GAAG,GAAG,CAAvB,IAA4Bb,MAAM,CAACG,MAAP,CAAcO,IAAd,CAAmBF,GAAG,GAAG,CAAzB,CAA5B;AACD,OAbH;;AAgBA,UAAI,0BAAcZ,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACG,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD;AAhCmB,GAAP;AAAA,C", "sourcesContent": ["import { isNodePattern, throwError } from \"@jimp/utils\";\n\n/**\n * Displaces the image based on the provided displacement map\n * @param {object} map the source Jimp instance\n * @param {number} offset the maximum displacement value\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nexport default () => ({\n  displace(map, offset, cb) {\n    if (typeof map !== \"object\" || map.constructor !== this.constructor) {\n      return throwError.call(this, \"The source must be a Jimp image\", cb);\n    }\n\n    if (typeof offset !== \"number\") {\n      return throwError.call(this, \"factor must be a number\", cb);\n    }\n\n    const source = this.cloneQuiet();\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        let displacement = (map.bitmap.data[idx] / 256) * offset;\n        displacement = Math.round(displacement);\n\n        const ids = this.getPixelIndex(x + displacement, y);\n        this.bitmap.data[ids] = source.bitmap.data[idx];\n        this.bitmap.data[ids + 1] = source.bitmap.data[idx + 1];\n        this.bitmap.data[ids + 2] = source.bitmap.data[idx + 2];\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "file": "index.js"}