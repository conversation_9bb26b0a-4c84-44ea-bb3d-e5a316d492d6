{"name": "glob-to-regexp", "version": "0.4.1", "description": "Convert globs to regular expressions", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/fitzgen/glob-to-regexp.git"}, "keywords": ["regexp", "glob", "regexps", "regular expressions", "regular expression", "wildcard"], "author": "<PERSON> <<EMAIL>>", "license": "BSD-2-<PERSON><PERSON>"}