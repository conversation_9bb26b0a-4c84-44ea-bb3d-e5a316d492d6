{"name": "@jimp/plugin-gaussian", "version": "0.16.13", "description": "gaussian blur an image.", "main": "dist/index.js", "module": "es/index.js", "types": "index.d.ts", "scripts": {"build": "npm run build:node:production && npm run build:module", "build:watch": "npm run build:node:debug -- -- --watch --verbose", "build:debug": "npm run build:node:debug", "build:module": "cross-env BABEL_ENV=module babel src -d es --source-maps --config-file ../../babel.config.js", "build:node": "babel src -d dist --source-maps --config-file ../../babel.config.js", "build:node:debug": "cross-env BABEL_ENV=development npm run build:node", "build:node:production": "cross-env BABEL_ENV=production npm run build:node"}, "author": "", "license": "MIT", "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.16.13"}, "peerDependencies": {"@jimp/custom": ">=0.3.5"}, "publishConfig": {"access": "public"}, "gitHead": "9eec86086634329a072901dca3a800b850548572"}