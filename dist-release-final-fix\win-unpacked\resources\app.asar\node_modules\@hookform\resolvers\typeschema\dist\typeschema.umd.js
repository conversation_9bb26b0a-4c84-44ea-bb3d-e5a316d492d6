!function(e,o){"object"==typeof exports&&"undefined"!=typeof module?o(exports,require("@hookform/resolvers"),require("@typeschema/main"),require("react-hook-form")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers","@typeschema/main","react-hook-form"],o):o((e||self).hookformResolversTypeschema={},e.hookformResolvers,e.main,e.ReactHookForm)}(this,function(e,o,r,s){var t=function(e,o){for(var r={};e.length;){var t=e[0];if(t.path){var a=t.path.join(".");if(r[a]||(r[a]={message:t.message,type:""}),o){var i=r[a].types,n=i&&i[""];r[a]=s.appendErrors(a,o,r,"",n?[].concat(n,t.message):t.message)}e.shift()}}return r};e.typeschemaResolver=function(e,s,a){return void 0===a&&(a={}),function(s,i,n){try{return Promise.resolve(r.validate(e,s)).then(function(e){return n.shouldUseNativeValidation&&o.validateFieldsNatively({},n),e.success?{errors:{},values:a.raw?s:e.data}:{values:{},errors:o.toNestErrors(t(e.issues,!n.shouldUseNativeValidation&&"all"===n.criteriaMode),n)}})}catch(e){return Promise.reject(e)}}}});
//# sourceMappingURL=typeschema.umd.js.map
