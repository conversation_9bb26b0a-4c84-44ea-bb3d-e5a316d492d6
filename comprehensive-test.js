const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔬 游戏修改器盒子 - 综合功能测试');
console.log('=====================================\n');

// 测试配置
const testConfig = {
  appPath: 'dist-release-fixed-final/win-unpacked/游戏修改器盒子.exe',
  portablePath: 'dist-release-fixed-final/游戏修改器盒子-便携版.exe',
  resourcesPath: 'dist-release-fixed-final/win-unpacked/resources',
  timeout: 30000 // 30秒超时
};

// 检查文件存在性
function checkFileExists(filePath, description) {
  const exists = fs.existsSync(filePath);
  console.log(`${exists ? '✅' : '❌'} ${description}: ${filePath}`);
  return exists;
}

// 检查目录内容
function checkDirectoryContents(dirPath, description) {
  if (!fs.existsSync(dirPath)) {
    console.log(`❌ ${description} 目录不存在: ${dirPath}`);
    return [];
  }
  
  try {
    const contents = fs.readdirSync(dirPath);
    console.log(`✅ ${description} 包含 ${contents.length} 个文件:`);
    contents.forEach(item => {
      const itemPath = path.join(dirPath, item);
      const isDir = fs.statSync(itemPath).isDirectory();
      console.log(`    ${isDir ? '📁' : '📄'} ${item}`);
    });
    return contents;
  } catch (err) {
    console.log(`❌ 读取 ${description} 失败: ${err.message}`);
    return [];
  }
}

// 启动应用程序进行测试
function startAppTest(appPath, testName) {
  return new Promise((resolve) => {
    console.log(`\n🚀 启动 ${testName}...`);
    console.log(`路径: ${appPath}`);
    
    if (!fs.existsSync(appPath)) {
      console.log(`❌ 应用程序文件不存在`);
      resolve({ success: false, error: '文件不存在' });
      return;
    }
    
    const startTime = Date.now();
    const app = spawn(appPath, [], {
      stdio: ['pipe', 'pipe', 'pipe'],
      detached: false
    });
    
    let stdout = '';
    let stderr = '';
    let hasOutput = false;
    
    app.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      hasOutput = true;
      console.log(`[${testName} STDOUT] ${output.trim()}`);
    });
    
    app.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      hasOutput = true;
      console.log(`[${testName} STDERR] ${output.trim()}`);
    });
    
    app.on('close', (code) => {
      const duration = Date.now() - startTime;
      console.log(`\n📊 ${testName} 测试结果:`);
      console.log(`  - 退出代码: ${code}`);
      console.log(`  - 运行时间: ${duration}ms`);
      console.log(`  - 有输出: ${hasOutput ? '是' : '否'}`);
      
      resolve({
        success: code === 0 || hasOutput,
        code,
        duration,
        stdout,
        stderr,
        hasOutput
      });
    });
    
    app.on('error', (err) => {
      console.log(`❌ ${testName} 启动失败: ${err.message}`);
      resolve({ success: false, error: err.message });
    });
    
    // 设置超时
    setTimeout(() => {
      if (!app.killed) {
        console.log(`⏰ ${testName} 测试超时，终止进程...`);
        app.kill();
      }
    }, testConfig.timeout);
    
    // 给应用程序一些时间启动
    setTimeout(() => {
      if (!app.killed) {
        console.log(`⏹️ ${testName} 正常运行，主动关闭测试...`);
        app.kill();
      }
    }, 5000); // 5秒后关闭
  });
}

// 主测试函数
async function runComprehensiveTest() {
  console.log('1️⃣ 检查文件完整性...\n');
  
  // 检查主要文件
  const appExists = checkFileExists(testConfig.appPath, '解包版应用程序');
  const portableExists = checkFileExists(testConfig.portablePath, '便携版应用程序');
  
  // 检查资源文件
  console.log('\n2️⃣ 检查资源文件...\n');
  const chromiumPath = path.join(testConfig.resourcesPath, 'chromium', 'chrome.exe');
  const scraperPath = path.join(testConfig.resourcesPath, 'scraper');
  
  checkFileExists(chromiumPath, 'Chromium浏览器');
  checkDirectoryContents(scraperPath, '爬虫脚本目录');
  
  // 检查关键脚本
  const requiredScripts = ['scraper.js', 'detail-scraper.js', 'search-scraper.js', 'translator.js'];
  console.log('\n3️⃣ 检查关键脚本文件...\n');
  requiredScripts.forEach(script => {
    const scriptPath = path.join(scraperPath, script);
    checkFileExists(scriptPath, script);
  });
  
  // 应用程序启动测试
  console.log('\n4️⃣ 应用程序启动测试...\n');
  
  if (appExists) {
    const appResult = await startAppTest(testConfig.appPath, '解包版');
    console.log(`解包版测试: ${appResult.success ? '✅ 通过' : '❌ 失败'}`);
  }
  
  if (portableExists) {
    const portableResult = await startAppTest(testConfig.portablePath, '便携版');
    console.log(`便携版测试: ${portableResult.success ? '✅ 通过' : '❌ 失败'}`);
  }
  
  // 总结
  console.log('\n📋 测试总结');
  console.log('=============');
  console.log('✅ 修复内容:');
  console.log('  - 子进程启动方式 (fork → spawn)');
  console.log('  - Node.js路径解析');
  console.log('  - 环境变量配置');
  console.log('  - 脚本路径解析');
  
  console.log('\n🎯 下一步手动测试:');
  console.log('  1. 启动应用程序');
  console.log('  2. 等待首页加载完成');
  console.log('  3. 测试搜索功能');
  console.log('  4. 测试下载选项获取');
  console.log('  5. 检查开发者工具控制台是否有错误');
}

// 运行测试
runComprehensiveTest().catch(console.error);
