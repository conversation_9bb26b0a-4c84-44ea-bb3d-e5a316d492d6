{"version": 3, "file": "vine.modern.mjs", "sources": ["../src/vine.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { SimpleErrorReporter, errors } from '@vinejs/vine';\nimport { FieldError, FieldErrors, appendErrors } from 'react-hook-form';\nimport type { Resolver } from './types';\n\nconst parseErrorSchema = (\n  vineErrors: SimpleErrorReporter['errors'],\n  validateAllFieldCriteria: boolean,\n) => {\n  const schemaErrors: Record<string, FieldError> = {};\n\n  for (; vineErrors.length; ) {\n    const error = vineErrors[0];\n    const path = error.field;\n\n    if (!(path in schemaErrors)) {\n      schemaErrors[path] = { message: error.message, type: error.rule };\n    }\n\n    if (validateAllFieldCriteria) {\n      const { types } = schemaErrors[path];\n      const messages = types && types[error.rule];\n\n      schemaErrors[path] = appendErrors(\n        path,\n        validateAllFieldCriteria,\n        schemaErrors,\n        error.rule,\n        messages ? [...(messages as string[]), error.message] : error.message,\n      ) as FieldError;\n    }\n\n    vineErrors.shift();\n  }\n\n  return schemaErrors;\n};\n\nexport const vineResolver: Resolver =\n  (schema, schemaOptions, resolverOptions = {}) =>\n  async (values, _, options) => {\n    try {\n      const data = await schema.validate(values, schemaOptions);\n\n      options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n      return {\n        errors: {} as FieldErrors,\n        values: resolverOptions.raw ? values : data,\n      };\n    } catch (error: any) {\n      if (error instanceof errors.E_VALIDATION_ERROR) {\n        return {\n          values: {},\n          errors: toNestErrors(\n            parseErrorSchema(\n              error.messages,\n              !options.shouldUseNativeValidation &&\n                options.criteriaMode === 'all',\n            ),\n            options,\n          ),\n        };\n      }\n\n      throw error;\n    }\n  };\n"], "names": ["parseErrorSchema", "vineErrors", "validateAllFieldCriteria", "schemaErrors", "length", "error", "path", "field", "message", "type", "rule", "types", "messages", "appendErrors", "shift", "vineResolver", "schema", "schemaOptions", "resolverOptions", "async", "values", "_", "options", "data", "validate", "shouldUseNativeValidation", "validateFieldsNatively", "errors", "raw", "E_VALIDATION_ERROR", "toNestErrors", "criteriaMode"], "mappings": "oKAKA,MAAMA,EAAmBA,CACvBC,EACAC,KAEA,MAAMC,EAA2C,CAAE,EAEnD,KAAOF,EAAWG,QAAU,CAC1B,MAAMC,EAAQJ,EAAW,GACnBK,EAAOD,EAAME,MAMnB,GAJMD,KAAQH,IACZA,EAAaG,GAAQ,CAAEE,QAASH,EAAMG,QAASC,KAAMJ,EAAMK,OAGzDR,EAA0B,CAC5B,MAAMS,MAAEA,GAAUR,EAAaG,GACzBM,EAAWD,GAASA,EAAMN,EAAMK,MAEtCP,EAAaG,GAAQO,EACnBP,EACAJ,EACAC,EACAE,EAAMK,KACNE,EAAW,IAAKA,EAAuBP,EAAMG,SAAWH,EAAMG,QAElE,CAEAP,EAAWa,OACb,CAEA,OAAOX,GAGIY,EACXA,CAACC,EAAQC,EAAeC,EAAkB,CAAE,IAC5CC,MAAOC,EAAQC,EAAGC,KAChB,IACE,MAAMC,QAAaP,EAAOQ,SAASJ,EAAQH,GAI3C,OAFAK,EAAQG,2BAA6BC,EAAuB,CAAA,EAAIJ,GAEzD,CACLK,OAAQ,GACRP,OAAQF,EAAgBU,IAAMR,EAASG,EAE3C,CAAE,MAAOlB,GACP,GAAIA,aAAiBsB,EAAOE,mBAC1B,MAAO,CACLT,OAAQ,CAAA,EACRO,OAAQG,EACN9B,EACEK,EAAMO,UACLU,EAAQG,2BACkB,QAAzBH,EAAQS,cAEZT,IAKN,MAAMjB,CACR"}