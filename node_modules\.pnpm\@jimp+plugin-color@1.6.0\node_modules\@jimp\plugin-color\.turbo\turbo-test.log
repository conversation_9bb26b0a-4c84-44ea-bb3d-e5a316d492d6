

> @jimp/plugin-color@1.1.2 test /Users/<USER>/Documents/jimp/plugins/plugin-color
> vitest "--watch=false"


[7m[1m[36m RUN [39m[22m[27m [36mv2.0.5[39m [90m/Users/<USER>/Documents/jimp/plugins/plugin-color[39m

[?25l [90m·[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [90m·[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [90m·[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[G [90m·[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
[?25l[?25l[?25l[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [90m·[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
[?25l[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠦[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠧[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠇[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [90m·[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠏[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [90m·[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠋[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [90m·[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [90m·[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [90m·[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [90m·[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠦[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠧[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠇[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠏[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠦[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠋[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠧[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠇[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠏[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠋[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠦[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠧[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠇[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠏[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠦[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠋[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠧[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠇[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠏[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠋[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠦[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠧[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠇[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠏[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠦[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠋[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠧[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠇[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠏[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠋[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠦[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠧[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠇[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m Brightness[2m (1)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠏[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠋[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠦[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠧[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠇[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠏[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠋[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠦[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠧[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠇[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠏[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠋[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠦[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠧[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠇[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠏[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠋[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠦[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠧[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠇[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠏[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠋[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠦[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠧[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠇[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠏[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠋[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠦[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠧[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠇[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠏[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠋[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠦[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠧[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠇[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠏[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠋[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠦[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠧[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠇[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠏[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠋[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠦[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠧[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠇[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠏[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠋[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠦[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠧[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠇[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠏[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠋[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠙[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠹[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠸[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠼[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠴[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠦[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠧[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠇[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [33m❯[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m
   [33m❯[39m Convolution[2m (2)[22m
     [33m⠏[39m 3x3 box blur matrix using convolute
     [90m·[39m new pixel value is greater than 255
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m[33m 3352[2mms[22m[39m
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mbirghtness[2m.node.test.ts[22m[2m (1)[22m[33m 915[2mms[22m[39m
 [32m✓[39m [2msrc/[22mconvolution[2m.node.test.ts[22m[2m (2)[22m[33m 3352[2mms[22m[39m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
 [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m

[2m Test Files [22m [1m[32m5 passed[39m[22m[90m (5)[39m
[2m      Tests [22m [1m[32m15 passed[39m[22m[90m (15)[39m
[2m   Start at [22m 01:34:00
[2m   Duration [22m 7.61s[2m (transform 1.49s, setup 0ms, collect 8.92s, tests 4.40s, environment 1ms, prepare 2.39s)[22m

[?25h[?25h
