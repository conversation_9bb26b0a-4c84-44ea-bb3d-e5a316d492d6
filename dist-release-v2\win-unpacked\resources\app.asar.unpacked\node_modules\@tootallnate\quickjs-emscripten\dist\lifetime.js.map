{"version": 3, "file": "lifetime.js", "sourceRoot": "", "sources": ["../ts/lifetime.ts"], "names": [], "mappings": ";;;AAAA,yDAAgE;AAChE,mCAAmC;AACnC,qCAA8C;AAqB9C;;;;;GAKG;AACH,MAAa,QAAQ;IAInB;;;;;;;OAOG;IACH,YACqB,MAAS,EACT,MAAoC,EACpC,QAAqC,EACrC,MAAc;QAHd,WAAM,GAAN,MAAM,CAAG;QACT,WAAM,GAAN,MAAM,CAA8B;QACpC,aAAQ,GAAR,QAAQ,CAA6B;QACrC,WAAM,GAAN,MAAM,CAAQ;QAfzB,WAAM,GAAY,IAAI,CAAA;QACtB,sBAAiB,GAAG,iBAAS,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAA;IAe1F,CAAC;IAEJ,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAA;IACpB,CAAC;IAED;;;;;OAKG;IACH,IAAI,KAAK;QACP,IAAI,CAAC,WAAW,EAAE,CAAA;QAClB,OAAO,IAAI,CAAC,MAAM,CAAA;IACpB,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAA;IACpB,CAAC;IAED,IAAI,OAAO;QACT,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAA;IACtB,CAAC;IAED;;OAEG;IACH,GAAG;QACD,IAAI,CAAC,WAAW,EAAE,CAAA;QAClB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAA;SACxC;QACD,OAAO,IAAI,QAAQ,CACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EACxB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,MAAM,CACZ,CAAA;IACH,CAAC;IAUD,OAAO,CAAI,GAAyB;QAClC,IAAI,CAAC,WAAW,EAAE,CAAA;QAClB,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,CAAA;QACxB,IAAI,CAAC,OAAO,EAAE,CAAA;QACd,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,CAAC,WAAW,EAAE,CAAA;QAClB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;SAC3B;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;IACrB,CAAC;IAEO,WAAW;QACjB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACf,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,MAAM,IAAI,4BAAmB,CAC3B,uBAAuB,IAAI,CAAC,iBAAiB,iBAAiB,CAC/D,CAAA;aACF;YACD,MAAM,IAAI,4BAAmB,CAAC,oBAAoB,CAAC,CAAA;SACpD;IACH,CAAC;CACF;AA9FD,4BA8FC;AAED;;GAEG;AACH,MAAa,cAAiC,SAAQ,QAAqB;IACzE,YAAY,KAAQ,EAAE,KAAa;QACjC,KAAK,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAA;IAC3C,CAAC;IAED,uDAAuD;IACvD,IAAI,OAAO;QACT,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kCAAkC;IAClC,GAAG;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,wBAAwB;IACxB,OAAO,KAAI,CAAC;CACb;AAjBD,wCAiBC;AAED;;;;;;GAMG;AACH,MAAa,YAA8C,SAAQ,QAAyB;IAC1F,YACE,KAAQ,EACR,MAAoC,EACpC,QAAiC,EACjC,KAAa;QAEb,0DAA0D;QAC1D,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,QAAsC,EAAE,KAAK,CAAC,CAAA;IACrE,CAAC;IAED,OAAO;QACL,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;IACrB,CAAC;CACF;AAdD,oCAcC;AAED,SAAS,YAAY,CAAC,KAAY,EAAE,UAA6B;IAC/D,iDAAiD;IACjD,IAAI,YAA+B,CAAA;IACnC,IAAI;QACF,KAAK,CAAC,OAAO,EAAE,CAAA;KAChB;IAAC,OAAO,KAAK,EAAE;QACd,YAAY,GAAG,KAAY,CAAA;KAC5B;IAED,IAAI,UAAU,IAAI,YAAY,EAAE;QAC9B,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE;YACxB,OAAO,EAAE,GAAG,UAAU,CAAC,OAAO,qCAAqC,YAAY,CAAC,OAAO,EAAE;YACzF,YAAY;SACb,CAAC,CAAA;QACF,MAAM,UAAU,CAAA;KACjB;IAED,IAAI,UAAU,IAAI,YAAY,EAAE;QAC9B,MAAM,UAAU,IAAI,YAAY,CAAA;KACjC;AACH,CAAC;AAED;;;GAGG;AACH,MAAa,KAAK;IAAlB;QA0DU,iBAAY,GAA8B,IAAI,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC,CAAA;IAuB3E,CAAC;IAhFC;;;;;;OAMG;IACH,MAAM,CAAC,SAAS,CAAI,KAA0B;QAC5C,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAA;QACzB,IAAI,UAA6B,CAAA;QACjC,IAAI;YACF,OAAO,KAAK,CAAC,KAAK,CAAC,CAAA;SACpB;QAAC,OAAO,KAAK,EAAE;YACd,UAAU,GAAG,KAAY,CAAA;YACzB,MAAM,KAAK,CAAA;SACZ;gBAAS;YACR,YAAY,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;SAChC;IACH,CAAC;IAED,MAAM,CAAC,mBAAmB,CACxB,KAAW,EACX,KAAsD;QAEtD,OAAO,IAAA,6BAAU,EAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,OAAO;YAC7C,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAA;YACzB,IAAI,UAA6B,CAAA;YACjC,IAAI;gBACF,OAAO,KAAK,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAA;aAC5D;YAAC,OAAO,KAAK,EAAE;gBACd,UAAU,GAAG,KAAY,CAAA;gBACzB,MAAM,KAAK,CAAA;aACZ;oBAAS;gBACR,YAAY,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;aAChC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,KAAK,CAAC,cAAc,CAAI,KAAmC;QAChE,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAA;QACzB,IAAI,UAA6B,CAAA;QACjC,IAAI;YACF,OAAO,MAAM,KAAK,CAAC,KAAK,CAAC,CAAA;SAC1B;QAAC,OAAO,KAAK,EAAE;YACd,UAAU,GAAG,KAAY,CAAA;YACzB,MAAM,KAAK,CAAA;SACZ;gBAAS;YACR,YAAY,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;SAChC;IACH,CAAC;IAID;;OAEG;IACH,MAAM,CAAuB,QAAW;QACtC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACrC,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAA;IAChC,CAAC;IAED,OAAO;QACL,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,EAAE,CAAA;QACxE,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;YAChC,IAAI,QAAQ,CAAC,KAAK,EAAE;gBAClB,QAAQ,CAAC,OAAO,EAAE,CAAA;aACnB;SACF;QACD,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;IAC7B,CAAC;CACF;AAjFD,sBAiFC", "sourcesContent": ["import { maybeAsync, <PERSON><PERSON><PERSON><PERSON><PERSON> } from \"./asyncify-helpers\"\nimport { QTS_DEBUG } from \"./debug\"\nimport { QuickJSUseAfterFree } from \"./errors\"\nimport type { QuickJSHandle } from \"./types\"\n\n/**\n * An object that can be disposed.\n * [[Lifetime]] is the canonical implementation of Disposable.\n * Use [[Scope]] to manage cleaning up multiple disposables.\n */\nexport interface Disposable {\n  /**\n   * Dispose of the underlying resources used by this object.\n   */\n  dispose(): void\n\n  /**\n   * @returns true if the object is alive\n   * @returns false after the object has been [[dispose]]d\n   */\n  alive: boolean\n}\n\n/**\n * A lifetime prevents access to a value after the lifetime has been\n * [[dispose]]ed.\n *\n * Typically, quickjs-emscripten uses Lifetimes to protect C memory pointers.\n */\nexport class Lifetime<T, TCopy = never, Owner = never> implements Disposable {\n  protected _alive: boolean = true\n  protected _constructorStack = QTS_DEBUG ? new Error(\"Lifetime constructed\").stack : undefined\n\n  /**\n   * When the Lifetime is disposed, it will call `disposer(_value)`. Use the\n   * disposer function to implement whatever cleanup needs to happen at the end\n   * of `value`'s lifetime.\n   *\n   * `_owner` is not used or controlled by the lifetime. It's just metadata for\n   * the creator.\n   */\n  constructor(\n    protected readonly _value: T,\n    protected readonly copier?: (value: T | TCopy) => TCopy,\n    protected readonly disposer?: (value: T | TCopy) => void,\n    protected readonly _owner?: Owner\n  ) {}\n\n  get alive() {\n    return this._alive\n  }\n\n  /**\n   * The value this Lifetime protects. You must never retain the value - it\n   * may become invalid, leading to memory errors.\n   *\n   * @throws If the lifetime has been [[dispose]]d already.\n   */\n  get value() {\n    this.assertAlive()\n    return this._value\n  }\n\n  get owner() {\n    return this._owner\n  }\n\n  get dupable() {\n    return !!this.copier\n  }\n\n  /**\n   * Create a new handle pointing to the same [[value]].\n   */\n  dup() {\n    this.assertAlive()\n    if (!this.copier) {\n      throw new Error(\"Non-dupable lifetime\")\n    }\n    return new Lifetime<TCopy, TCopy, Owner>(\n      this.copier(this._value),\n      this.copier,\n      this.disposer,\n      this._owner\n    )\n  }\n\n  /**\n   * Call `map` with this lifetime, then dispose the lifetime.\n   * @return the result of `map(this)`.\n   */\n  consume<O>(map: (lifetime: this) => O): O\n  // A specific type definition is needed for our common use-case\n  // https://github.com/microsoft/TypeScript/issues/30271\n  consume<O>(map: (lifetime: QuickJSHandle) => O): O\n  consume<O>(map: (lifetime: any) => O): O {\n    this.assertAlive()\n    const result = map(this)\n    this.dispose()\n    return result\n  }\n\n  /**\n   * Dispose of [[value]] and perform cleanup.\n   */\n  dispose() {\n    this.assertAlive()\n    if (this.disposer) {\n      this.disposer(this._value)\n    }\n    this._alive = false\n  }\n\n  private assertAlive() {\n    if (!this.alive) {\n      if (this._constructorStack) {\n        throw new QuickJSUseAfterFree(\n          `Lifetime not alive\\n${this._constructorStack}\\nLifetime used`\n        )\n      }\n      throw new QuickJSUseAfterFree(\"Lifetime not alive\")\n    }\n  }\n}\n\n/**\n * A Lifetime that lives forever. Used for constants.\n */\nexport class StaticLifetime<T, Owner = never> extends Lifetime<T, T, Owner> {\n  constructor(value: T, owner?: Owner) {\n    super(value, undefined, undefined, owner)\n  }\n\n  // Static lifetime doesn't need a copier to be copiable\n  get dupable() {\n    return true\n  }\n\n  // Copy returns the same instance.\n  dup() {\n    return this\n  }\n\n  // Dispose does nothing.\n  dispose() {}\n}\n\n/**\n * A Lifetime that does not own its `value`. A WeakLifetime never calls its\n * `disposer` function, but can be `dup`ed to produce regular lifetimes that\n * do.\n *\n * Used for function arguments.\n */\nexport class WeakLifetime<T, TCopy = never, Owner = never> extends Lifetime<T, TCopy, Owner> {\n  constructor(\n    value: T,\n    copier?: (value: T | TCopy) => TCopy,\n    disposer?: (value: TCopy) => void,\n    owner?: Owner\n  ) {\n    // We don't care if the disposer doesn't support freeing T\n    super(value, copier, disposer as (value: T | TCopy) => void, owner)\n  }\n\n  dispose() {\n    this._alive = false\n  }\n}\n\nfunction scopeFinally(scope: Scope, blockError: Error | undefined) {\n  // console.log('scopeFinally', scope, blockError)\n  let disposeError: Error | undefined\n  try {\n    scope.dispose()\n  } catch (error) {\n    disposeError = error as any\n  }\n\n  if (blockError && disposeError) {\n    Object.assign(blockError, {\n      message: `${blockError.message}\\n Then, failed to dispose scope: ${disposeError.message}`,\n      disposeError,\n    })\n    throw blockError\n  }\n\n  if (blockError || disposeError) {\n    throw blockError || disposeError\n  }\n}\n\n/**\n * Scope helps reduce the burden of manually tracking and disposing of\n * Lifetimes. See [[withScope]]. and [[withScopeAsync]].\n */\nexport class Scope implements Disposable {\n  /**\n   * Run `block` with a new Scope instance that will be disposed after the block returns.\n   * Inside `block`, call `scope.manage` on each lifetime you create to have the lifetime\n   * automatically disposed after the block returns.\n   *\n   * @warning Do not use with async functions. Instead, use [[withScopeAsync]].\n   */\n  static withScope<R>(block: (scope: Scope) => R): R {\n    const scope = new Scope()\n    let blockError: Error | undefined\n    try {\n      return block(scope)\n    } catch (error) {\n      blockError = error as any\n      throw error\n    } finally {\n      scopeFinally(scope, blockError)\n    }\n  }\n\n  static withScopeMaybeAsync<Return, This, Yielded>(\n    _this: This,\n    block: MaybeAsyncBlock<Return, This, Yielded, [Scope]>\n  ): Return | Promise<Return> {\n    return maybeAsync(undefined, function* (awaited) {\n      const scope = new Scope()\n      let blockError: Error | undefined\n      try {\n        return yield* awaited.of(block.call(_this, awaited, scope))\n      } catch (error) {\n        blockError = error as any\n        throw error\n      } finally {\n        scopeFinally(scope, blockError)\n      }\n    })\n  }\n\n  /**\n   * Run `block` with a new Scope instance that will be disposed after the\n   * block's returned promise settles. Inside `block`, call `scope.manage` on each\n   * lifetime you create to have the lifetime automatically disposed after the\n   * block returns.\n   */\n  static async withScopeAsync<R>(block: (scope: Scope) => Promise<R>): Promise<R> {\n    const scope = new Scope()\n    let blockError: Error | undefined\n    try {\n      return await block(scope)\n    } catch (error) {\n      blockError = error as any\n      throw error\n    } finally {\n      scopeFinally(scope, blockError)\n    }\n  }\n\n  private _disposables: Lifetime<Set<Disposable>> = new Lifetime(new Set())\n\n  /**\n   * Track `lifetime` so that it is disposed when this scope is disposed.\n   */\n  manage<T extends Disposable>(lifetime: T): T {\n    this._disposables.value.add(lifetime)\n    return lifetime\n  }\n\n  get alive() {\n    return this._disposables.alive\n  }\n\n  dispose() {\n    const lifetimes = Array.from(this._disposables.value.values()).reverse()\n    for (const lifetime of lifetimes) {\n      if (lifetime.alive) {\n        lifetime.dispose()\n      }\n    }\n    this._disposables.dispose()\n  }\n}\n"]}