const { spawn } = require('child_process');
const path = require('path');

console.log('🧪 测试最终修复版本');
console.log('=====================================');

// 测试便携版
const portableExe = path.join(__dirname, 'dist-release-final-fix', '游戏修改器盒子-便携版.exe');

console.log(`📦 启动便携版: ${portableExe}`);

const testProcess = spawn(portableExe, [], {
  stdio: 'inherit',
  detached: true
});

testProcess.on('spawn', () => {
  console.log('✅ 应用启动成功！');
  console.log('');
  console.log('🔍 请测试以下功能：');
  console.log('1. 搜索功能 - 输入"黑神话悟空"或"Cyberpunk 2077"');
  console.log('2. 翻译功能 - 确认中文游戏名能正确翻译为英文');
  console.log('3. 下载功能 - 点击任意修改器的"立即下载"按钮');
  console.log('4. 检查开发者工具中的调试信息');
  console.log('');
  console.log('💡 如果功能正常，说明子进程问题已修复！');
  console.log('');
  console.log('⚠️  测试完成后请手动关闭应用');
});

testProcess.on('error', (error) => {
  console.error('❌ 启动失败:', error);
});

// 10秒后分离进程，让用户自己测试
setTimeout(() => {
  testProcess.unref();
  console.log('🔄 进程已分离，可以独立运行');
}, 10000);
