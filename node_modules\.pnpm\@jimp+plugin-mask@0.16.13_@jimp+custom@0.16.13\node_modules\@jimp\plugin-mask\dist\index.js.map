{"version": 3, "sources": ["../src/index.js"], "names": ["mask", "src", "x", "y", "cb", "constructor", "throwError", "call", "Math", "round", "w", "bitmap", "width", "h", "height", "baseImage", "scanQuiet", "sx", "sy", "idx", "destX", "destY", "dstIdx", "getPixelIndex", "data", "avg"], "mappings": ";;;;;;;AAAA;;AAEA;;;;;;;;eAQe;AAAA,SAAO;AACpBA,IAAAA,IADoB,gBACfC,GADe,EACQ;AAAA,UAAlBC,CAAkB,uEAAd,CAAc;AAAA,UAAXC,CAAW,uEAAP,CAAO;AAAA,UAAJC,EAAI;;AAC1B,UAAI,EAAEH,GAAG,YAAY,KAAKI,WAAtB,CAAJ,EAAwC;AACtC,eAAOC,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,iCAAtB,EAAyDH,EAAzD,CAAP;AACD;;AAED,UAAI,OAAOF,CAAP,KAAa,QAAb,IAAyB,OAAOC,CAAP,KAAa,QAA1C,EAAoD;AAClD,eAAOG,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,yBAAtB,EAAiDH,EAAjD,CAAP;AACD,OAPyB,CAS1B;;;AACAF,MAAAA,CAAC,GAAGM,IAAI,CAACC,KAAL,CAAWP,CAAX,CAAJ;AACAC,MAAAA,CAAC,GAAGK,IAAI,CAACC,KAAL,CAAWN,CAAX,CAAJ;AAEA,UAAMO,CAAC,GAAG,KAAKC,MAAL,CAAYC,KAAtB;AACA,UAAMC,CAAC,GAAG,KAAKF,MAAL,CAAYG,MAAtB;AACA,UAAMC,SAAS,GAAG,IAAlB;AAEAd,MAAAA,GAAG,CAACe,SAAJ,CACE,CADF,EAEE,CAFF,EAGEf,GAAG,CAACU,MAAJ,CAAWC,KAHb,EAIEX,GAAG,CAACU,MAAJ,CAAWG,MAJb,EAKE,UAAUG,EAAV,EAAcC,EAAd,EAAkBC,GAAlB,EAAuB;AACrB,YAAMC,KAAK,GAAGlB,CAAC,GAAGe,EAAlB;AACA,YAAMI,KAAK,GAAGlB,CAAC,GAAGe,EAAlB;;AAEA,YAAIE,KAAK,IAAI,CAAT,IAAcC,KAAK,IAAI,CAAvB,IAA4BD,KAAK,GAAGV,CAApC,IAAyCW,KAAK,GAAGR,CAArD,EAAwD;AACtD,cAAMS,MAAM,GAAGP,SAAS,CAACQ,aAAV,CAAwBH,KAAxB,EAA+BC,KAA/B,CAAf;AADsD,cAE9CG,IAF8C,GAErC,KAAKb,MAFgC,CAE9Ca,IAF8C;AAGtD,cAAMC,GAAG,GAAG,CAACD,IAAI,CAACL,GAAG,GAAG,CAAP,CAAJ,GAAgBK,IAAI,CAACL,GAAG,GAAG,CAAP,CAApB,GAAgCK,IAAI,CAACL,GAAG,GAAG,CAAP,CAArC,IAAkD,CAA9D;AAEAJ,UAAAA,SAAS,CAACJ,MAAV,CAAiBa,IAAjB,CAAsBF,MAAM,GAAG,CAA/B,KAAqCG,GAAG,GAAG,GAA3C;AACD;AACF,OAhBH;;AAmBA,UAAI,0BAAcrB,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACG,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD;AA1CmB,GAAP;AAAA,C", "sourcesContent": ["import { isNodePattern, throwError } from \"@jimp/utils\";\n\n/**\n * Masks a source image on to this image using average pixel colour. A completely black pixel on the mask will turn a pixel in the image completely transparent.\n * @param {Jimp} src the source Jimp instance\n * @param {number} x the horizontal position to blit the image\n * @param {number} y the vertical position to blit the image\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nexport default () => ({\n  mask(src, x = 0, y = 0, cb) {\n    if (!(src instanceof this.constructor)) {\n      return throwError.call(this, \"The source must be a Jimp image\", cb);\n    }\n\n    if (typeof x !== \"number\" || typeof y !== \"number\") {\n      return throwError.call(this, \"x and y must be numbers\", cb);\n    }\n\n    // round input\n    x = Math.round(x);\n    y = Math.round(y);\n\n    const w = this.bitmap.width;\n    const h = this.bitmap.height;\n    const baseImage = this;\n\n    src.scanQuiet(\n      0,\n      0,\n      src.bitmap.width,\n      src.bitmap.height,\n      function (sx, sy, idx) {\n        const destX = x + sx;\n        const destY = y + sy;\n\n        if (destX >= 0 && destY >= 0 && destX < w && destY < h) {\n          const dstIdx = baseImage.getPixelIndex(destX, destY);\n          const { data } = this.bitmap;\n          const avg = (data[idx + 0] + data[idx + 1] + data[idx + 2]) / 3;\n\n          baseImage.bitmap.data[dstIdx + 3] *= avg / 255;\n        }\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "file": "index.js"}