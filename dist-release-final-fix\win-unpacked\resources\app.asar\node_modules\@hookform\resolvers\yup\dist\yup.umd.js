!function(e,o){"object"==typeof exports&&"undefined"!=typeof module?o(exports,require("@hookform/resolvers"),require("react-hook-form")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers","react-hook-form"],o):o((e||self).hookformResolversYup={},e.hookformResolvers,e.ReactHookForm)}(this,function(e,o,t){e.yupResolver=function(e,r,n){return void 0===r&&(r={}),void 0===n&&(n={}),function(s,a,i){try{return Promise.resolve(function(t,u){try{var c=(r.context&&"development"===process.env.NODE_ENV&&console.warn("You should not used the yup options context. Please, use the 'useForm' context object instead"),Promise.resolve(e["sync"===n.mode?"validateSync":"validate"](s,Object.assign({abortEarly:!1},r,{context:a}))).then(function(e){return i.shouldUseNativeValidation&&o.validateFieldsNatively({},i),{values:n.raw?s:e,errors:{}}}))}catch(e){return u(e)}return c&&c.then?c.then(void 0,u):c}(0,function(e){if(!e.inner)throw e;return{values:{},errors:o.toNestErrors((r=e,n=!i.shouldUseNativeValidation&&"all"===i.criteriaMode,(r.inner||[]).reduce(function(e,o){if(e[o.path]||(e[o.path]={message:o.message,type:o.type}),n){var r=e[o.path].types,s=r&&r[o.type];e[o.path]=t.appendErrors(o.path,n,e,o.type,s?[].concat(s,o.message):o.message)}return e},{})),i)};var r,n}))}catch(e){return Promise.reject(e)}}}});
//# sourceMappingURL=yup.umd.js.map
