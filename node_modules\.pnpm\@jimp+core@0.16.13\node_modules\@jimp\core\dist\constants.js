"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.EDGE_CROP = exports.EDGE_WRAP = exports.EDGE_EXTEND = exports.BLEND_EXCLUSION = exports.BLEND_DIFFERENCE = exports.BLEND_HARDLIGHT = exports.BLE<PERSON>_LIGHTEN = exports.BLEND_DARKEN = exports.BLEND_OVERLAY = exports.BLEND_SCREEN = exports.BLEND_ADD = exports.BLEND_MULTIPLY = exports.BLEND_DESTINATION_OVER = exports.BLEND_SOURCE_OVER = exports.VERTICAL_ALIGN_BOTTOM = exports.VERTICAL_ALIGN_MIDDLE = exports.VERTICAL_ALIGN_TOP = exports.HORIZONTAL_ALIGN_RIGHT = exports.HORIZONTAL_ALIGN_CENTER = exports.HORIZONTAL_ALIGN_LEFT = exports.AUTO = void 0;
// used to auto resizing etc.
var AUTO = -1; // Align modes for cover, contain, bit masks

exports.AUTO = AUTO;
var HORIZONTAL_ALIGN_LEFT = 1;
exports.HORIZONTAL_ALIGN_LEFT = HORIZONTAL_ALIGN_LEFT;
var HORIZONTAL_ALIGN_CENTER = 2;
exports.HORIZONTAL_ALIGN_CENTER = HORIZONTAL_ALIGN_CENTER;
var HORIZONTAL_ALIGN_RIGHT = 4;
exports.HORIZONTAL_ALIGN_RIGHT = HORIZONTAL_ALIGN_RIGHT;
var VERTICAL_ALIGN_TOP = 8;
exports.VERTICAL_ALIGN_TOP = VERTICAL_ALIGN_TOP;
var VERTICAL_ALIGN_MIDDLE = 16;
exports.VERTICAL_ALIGN_MIDDLE = VERTICAL_ALIGN_MIDDLE;
var VERTICAL_ALIGN_BOTTOM = 32; // blend modes

exports.VERTICAL_ALIGN_BOTTOM = VERTICAL_ALIGN_BOTTOM;
var BLEND_SOURCE_OVER = "srcOver";
exports.BLEND_SOURCE_OVER = BLEND_SOURCE_OVER;
var BLEND_DESTINATION_OVER = "dstOver";
exports.BLEND_DESTINATION_OVER = BLEND_DESTINATION_OVER;
var BLEND_MULTIPLY = "multiply";
exports.BLEND_MULTIPLY = BLEND_MULTIPLY;
var BLEND_ADD = "add";
exports.BLEND_ADD = BLEND_ADD;
var BLEND_SCREEN = "screen";
exports.BLEND_SCREEN = BLEND_SCREEN;
var BLEND_OVERLAY = "overlay";
exports.BLEND_OVERLAY = BLEND_OVERLAY;
var BLEND_DARKEN = "darken";
exports.BLEND_DARKEN = BLEND_DARKEN;
var BLEND_LIGHTEN = "lighten";
exports.BLEND_LIGHTEN = BLEND_LIGHTEN;
var BLEND_HARDLIGHT = "hardLight";
exports.BLEND_HARDLIGHT = BLEND_HARDLIGHT;
var BLEND_DIFFERENCE = "difference";
exports.BLEND_DIFFERENCE = BLEND_DIFFERENCE;
var BLEND_EXCLUSION = "exclusion"; // Edge Handling

exports.BLEND_EXCLUSION = BLEND_EXCLUSION;
var EDGE_EXTEND = 1;
exports.EDGE_EXTEND = EDGE_EXTEND;
var EDGE_WRAP = 2;
exports.EDGE_WRAP = EDGE_WRAP;
var EDGE_CROP = 3;
exports.EDGE_CROP = EDGE_CROP;
//# sourceMappingURL=constants.js.map