!function(r,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("@hookform/resolvers"),require("react-hook-form")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers","react-hook-form"],e):e((r||self).hookformResolversZod={},r.hookformResolvers,r.ReactHookForm)}(this,function(r,e,o){var n=function(r,e){for(var n={};r.length;){var s=r[0],t=s.code,i=s.message,a=s.path.join(".");if(!n[a])if("unionErrors"in s){var u=s.unionErrors[0].errors[0];n[a]={message:u.message,type:u.code}}else n[a]={message:i,type:t};if("unionErrors"in s&&s.unionErrors.forEach(function(e){return e.errors.forEach(function(e){return r.push(e)})}),e){var f=n[a].types,c=f&&f[s.code];n[a]=o.appendErrors(a,e,n,t,c?[].concat(c,s.message):s.message)}r.shift()}return n};r.zodResolver=function(r,o,s){return void 0===s&&(s={}),function(t,i,a){try{return Promise.resolve(function(n,i){try{var u=Promise.resolve(r["sync"===s.mode?"parse":"parseAsync"](t,o)).then(function(r){return a.shouldUseNativeValidation&&e.validateFieldsNatively({},a),{errors:{},values:s.raw?t:r}})}catch(r){return i(r)}return u&&u.then?u.then(void 0,i):u}(0,function(r){if(function(r){return Array.isArray(null==r?void 0:r.errors)}(r))return{values:{},errors:e.toNestErrors(n(r.errors,!a.shouldUseNativeValidation&&"all"===a.criteriaMode),a)};throw r}))}catch(r){return Promise.reject(r)}}}});
//# sourceMappingURL=zod.umd.js.map
