{"version": 3, "file": "degenerator.js", "sourceRoot": "", "sources": ["../src/degenerator.ts"], "names": [], "mappings": ";;;AAAA,+BAA6B;AAC7B,yCAAqC;AACrC,qCAAsC;AACtC,yCAAkE;AAKlE;;;;;;;GAOG;AAEH,SAAgB,WAAW,CAAC,IAAY,EAAE,MAAwB;IACjE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QAC3B,MAAM,IAAI,SAAS,CAAC,gDAAgD,CAAC,CAAC;KACtE;IAED,uEAAuE;IACvE,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAE9B,MAAM,GAAG,GAAG,IAAA,qBAAW,EAAC,IAAI,CAAC,CAAC;IAE9B,yEAAyE;IACzE,uEAAuE;IACvE,6EAA6E;IAC7E,4EAA4E;IAC5E,wEAAwE;IACxE,IAAI,eAAe,GAAG,CAAC,CAAC;IACxB,GAAG;QACF,eAAe,GAAG,KAAK,CAAC,MAAM,CAAC;QAC/B,IAAA,iBAAK,EAAC,GAAG,EAAE;YACV,wBAAwB,CAAC,IAAI;gBAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;oBAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBACvD,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;wBAC9C,IACC,sBAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,WAAW,CAAC;4BACvC,sBAAC,CAAC,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC;4BACpC,sBAAC,CAAC,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC;4BAClC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC;4BACvC,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EACrC;4BACD,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;yBAChC;qBACD;iBACD;gBACD,OAAO,KAAK,CAAC;YACd,CAAC;YACD,yBAAyB,CAAC,IAAI;gBAC7B,IACC,sBAAC,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;oBAClC,sBAAC,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;oBACnC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC;oBACtC,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,EACrC;oBACD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBAChC;gBACD,OAAO,KAAK,CAAC;YACd,CAAC;YACD,aAAa,CAAC,IAAI;gBACjB,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;oBACjB,IAAI,gBAAgB,GAAG,KAAK,CAAC;oBAC7B,IAAA,iBAAK,EAAC,IAAI,CAAC,IAAI,EAAE;wBAChB,mBAAmB,CAAC,IAAI;4BACvB,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;gCACjC,gBAAgB,GAAG,IAAI,CAAC;6BACxB;4BACD,OAAO,KAAK,CAAC;wBACd,CAAC;qBACD,CAAC,CAAC;oBAEH,IAAI,CAAC,gBAAgB,EAAE;wBACtB,OAAO,KAAK,CAAC;qBACb;oBAED,yCAAyC;oBACzC,oCAAoC;oBACpC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;oBAEvB,qCAAqC;oBACrC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;wBACzC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;qBAC9B;iBACD;gBAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACrB,CAAC;SACD,CAAC,CAAC;KACH,QAAQ,eAAe,KAAK,KAAK,CAAC,MAAM,EAAE;IAE3C,+DAA+D;IAC/D,kDAAkD;IAClD,IAAA,iBAAK,EAAC,GAAG,EAAE;QACV,mBAAmB,CAAC,IAAI;YACvB,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;gBACjC,sCAAsC;gBACtC,yCAAyC;gBACzC,MAAM,QAAQ,GAAG,KAAK,CAAC;gBACvB,MAAM,EACL,IAAI,EACJ,MAAM,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,GACvB,GAAG,IAAI,CAAC;gBAET,MAAM,IAAI,GAAG,oBAAC,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAEpD,IAAI,sBAAC,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;oBAClC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;iBAC7B;qBAAM;oBACN,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;iBACnB;aACD;YAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;KACD,CAAC,CAAC;IAEH,OAAO,IAAA,oBAAQ,EAAC,GAAG,CAAC,CAAC;AACtB,CAAC;AAzGD,kCAyGC;AAED;;;;;;;;GAQG;AAEH,SAAS,UAAU,CAClB,EAAE,MAAM,EAAoB,EAC5B,KAAuB;IAEvB,IAAI,IAAY,CAAC;IACjB,IAAI,sBAAC,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;QAC/B,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;KACnB;SAAM,IAAI,sBAAC,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;QAC5C,IACC,sBAAC,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;YACjC,sBAAC,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,EAClC;YACD,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;SACvD;aAAM;YACN,OAAO,KAAK,CAAC;SACb;KACD;SAAM,IAAI,sBAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;QAC9C,IAAI,MAAM,CAAC,EAAE,EAAE;YACd,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC;SACtB;aAAM;YACN,OAAO,KAAK,CAAC;SACb;KACD;SAAM;QACN,MAAM,IAAI,KAAK,CAAC,mCAAmC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;KAClE;IACD,OAAO,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC/B,CAAC;AAED,SAAS,SAAS,CAAC,IAAY,EAAE,KAAuB;IACvD,+EAA+E;IAC/E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACtC,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACnB,IAAI,YAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YACtB,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACjB,OAAO,IAAI,CAAC;aACZ;SACD;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE;YACtB,OAAO,IAAI,CAAC;SACZ;KACD;IACD,OAAO,KAAK,CAAC;AACd,CAAC"}