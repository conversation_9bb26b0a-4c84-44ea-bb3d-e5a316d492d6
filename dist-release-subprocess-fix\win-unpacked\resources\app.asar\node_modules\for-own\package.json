{"name": "for-own", "description": "Iterate over the own enumerable properties of an object, and return an object with properties that evaluate to true from the callback. Exit early by returning `false`. JavaScript/Node.js.", "version": "0.1.5", "homepage": "https://github.com/jonschlinkert/for-own", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/for-own", "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "dependencies": {"for-in": "^1.0.1"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.2.0"}, "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["arr-flatten", "collection-map", "for-in"]}, "reflinks": ["verb"], "lint": {"reflinks": true}}}