{"version": 3, "sources": ["../src/index.js"], "names": ["histogram", "r", "Array", "fill", "g", "b", "scanQuiet", "bitmap", "width", "height", "x", "y", "index", "data", "normalize", "value", "min", "max", "getBounds", "histogramChannel", "findIndex", "slice", "reverse", "cb", "h", "call", "bounds", "idx"], "mappings": ";;;;;;;AAAA;;AAEA;;;;AAIA,SAASA,SAAT,GAAqB;AACnB,MAAMA,SAAS,GAAG;AAChBC,IAAAA,CAAC,EAAE,IAAIC,KAAJ,CAAU,GAAV,EAAeC,IAAf,CAAoB,CAApB,CADa;AAEhBC,IAAAA,CAAC,EAAE,IAAIF,KAAJ,CAAU,GAAV,EAAeC,IAAf,CAAoB,CAApB,CAFa;AAGhBE,IAAAA,CAAC,EAAE,IAAIH,KAAJ,CAAU,GAAV,EAAeC,IAAf,CAAoB,CAApB;AAHa,GAAlB;AAMA,OAAKG,SAAL,CACE,CADF,EAEE,CAFF,EAGE,KAAKC,MAAL,CAAYC,KAHd,EAIE,KAAKD,MAAL,CAAYE,MAJd,EAKE,UAAUC,CAAV,EAAaC,CAAb,EAAgBC,KAAhB,EAAuB;AACrBZ,IAAAA,SAAS,CAACC,CAAV,CAAY,KAAKM,MAAL,CAAYM,IAAZ,CAAiBD,KAAK,GAAG,CAAzB,CAAZ;AACAZ,IAAAA,SAAS,CAACI,CAAV,CAAY,KAAKG,MAAL,CAAYM,IAAZ,CAAiBD,KAAK,GAAG,CAAzB,CAAZ;AACAZ,IAAAA,SAAS,CAACK,CAAV,CAAY,KAAKE,MAAL,CAAYM,IAAZ,CAAiBD,KAAK,GAAG,CAAzB,CAAZ;AACD,GATH;AAYA,SAAOZ,SAAP;AACD;AAED;;;;;;;;;AAOA,IAAMc,UAAS,GAAG,SAAZA,SAAY,CAAUC,KAAV,EAAiBC,GAAjB,EAAsBC,GAAtB,EAA2B;AAC3C,SAAQ,CAACF,KAAK,GAAGC,GAAT,IAAgB,GAAjB,IAAyBC,GAAG,GAAGD,GAA/B,CAAP;AACD,CAFD;;AAIA,IAAME,SAAS,GAAG,SAAZA,SAAY,CAAUC,gBAAV,EAA4B;AAC5C,SAAO,CACLA,gBAAgB,CAACC,SAAjB,CAA2B,UAACL,KAAD;AAAA,WAAWA,KAAK,GAAG,CAAnB;AAAA,GAA3B,CADK,EAEL,MACEI,gBAAgB,CACbE,KADH,GAEGC,OAFH,GAGGF,SAHH,CAGa,UAACL,KAAD;AAAA,WAAWA,KAAK,GAAG,CAAnB;AAAA,GAHb,CAHG,CAAP;AAQD,CATD;AAWA;;;;;;;eAKe;AAAA,SAAO;AACpBD,IAAAA,SADoB,qBACVS,EADU,EACN;AACZ,UAAMC,CAAC,GAAGxB,SAAS,CAACyB,IAAV,CAAe,IAAf,CAAV,CADY,CAGZ;;AACA,UAAMC,MAAM,GAAG;AACbzB,QAAAA,CAAC,EAAEiB,SAAS,CAACM,CAAC,CAACvB,CAAH,CADC;AAEbG,QAAAA,CAAC,EAAEc,SAAS,CAACM,CAAC,CAACpB,CAAH,CAFC;AAGbC,QAAAA,CAAC,EAAEa,SAAS,CAACM,CAAC,CAACnB,CAAH;AAHC,OAAf,CAJY,CAUZ;;AACA,WAAKC,SAAL,CACE,CADF,EAEE,CAFF,EAGE,KAAKC,MAAL,CAAYC,KAHd,EAIE,KAAKD,MAAL,CAAYE,MAJd,EAKE,UAAUC,CAAV,EAAaC,CAAb,EAAgBgB,GAAhB,EAAqB;AACnB,YAAM1B,CAAC,GAAG,KAAKM,MAAL,CAAYM,IAAZ,CAAiBc,GAAG,GAAG,CAAvB,CAAV;AACA,YAAMvB,CAAC,GAAG,KAAKG,MAAL,CAAYM,IAAZ,CAAiBc,GAAG,GAAG,CAAvB,CAAV;AACA,YAAMtB,CAAC,GAAG,KAAKE,MAAL,CAAYM,IAAZ,CAAiBc,GAAG,GAAG,CAAvB,CAAV;AAEA,aAAKpB,MAAL,CAAYM,IAAZ,CAAiBc,GAAG,GAAG,CAAvB,IAA4Bb,UAAS,CAACb,CAAD,EAAIyB,MAAM,CAACzB,CAAP,CAAS,CAAT,CAAJ,EAAiByB,MAAM,CAACzB,CAAP,CAAS,CAAT,CAAjB,CAArC;AACA,aAAKM,MAAL,CAAYM,IAAZ,CAAiBc,GAAG,GAAG,CAAvB,IAA4Bb,UAAS,CAACV,CAAD,EAAIsB,MAAM,CAACtB,CAAP,CAAS,CAAT,CAAJ,EAAiBsB,MAAM,CAACtB,CAAP,CAAS,CAAT,CAAjB,CAArC;AACA,aAAKG,MAAL,CAAYM,IAAZ,CAAiBc,GAAG,GAAG,CAAvB,IAA4Bb,UAAS,CAACT,CAAD,EAAIqB,MAAM,CAACrB,CAAP,CAAS,CAAT,CAAJ,EAAiBqB,MAAM,CAACrB,CAAP,CAAS,CAAT,CAAjB,CAArC;AACD,OAbH;;AAgBA,UAAI,0BAAckB,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACE,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD;AAjCmB,GAAP;AAAA,C", "sourcesContent": ["import { isNodePattern } from \"@jimp/utils\";\n\n/**\n * Get an image's histogram\n * @return {object} An object with an array of color occurrence counts for each channel (r,g,b)\n */\nfunction histogram() {\n  const histogram = {\n    r: new Array(256).fill(0),\n    g: new Array(256).fill(0),\n    b: new Array(256).fill(0),\n  };\n\n  this.scanQuiet(\n    0,\n    0,\n    this.bitmap.width,\n    this.bitmap.height,\n    function (x, y, index) {\n      histogram.r[this.bitmap.data[index + 0]]++;\n      histogram.g[this.bitmap.data[index + 1]]++;\n      histogram.b[this.bitmap.data[index + 2]]++;\n    }\n  );\n\n  return histogram;\n}\n\n/**\n * Normalize values\n * @param  {integer} value Pixel channel value.\n * @param  {integer} min   Minimum value for channel\n * @param  {integer} max   Maximum value for channel\n * @return {integer} normalized values\n */\nconst normalize = function (value, min, max) {\n  return ((value - min) * 255) / (max - min);\n};\n\nconst getBounds = function (histogramChannel) {\n  return [\n    histogramChannel.findIndex((value) => value > 0),\n    255 -\n      histogramChannel\n        .slice()\n        .reverse()\n        .findIndex((value) => value > 0),\n  ];\n};\n\n/**\n * Normalizes the image\n * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nexport default () => ({\n  normalize(cb) {\n    const h = histogram.call(this);\n\n    // store bounds (minimum and maximum values)\n    const bounds = {\n      r: getBounds(h.r),\n      g: getBounds(h.g),\n      b: getBounds(h.b),\n    };\n\n    // apply value transformations\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        const r = this.bitmap.data[idx + 0];\n        const g = this.bitmap.data[idx + 1];\n        const b = this.bitmap.data[idx + 2];\n\n        this.bitmap.data[idx + 0] = normalize(r, bounds.r[0], bounds.r[1]);\n        this.bitmap.data[idx + 1] = normalize(g, bounds.g[0], bounds.g[1]);\n        this.bitmap.data[idx + 2] = normalize(b, bounds.b[0], bounds.b[1]);\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "file": "index.js"}