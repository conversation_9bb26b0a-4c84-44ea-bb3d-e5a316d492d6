import{get as t,set as e}from"react-hook-form";const s=(e,s,o)=>{if(e&&"reportValidity"in e){const r=t(o,s);e.setCustomValidity(r&&r.message||""),e.reportValidity()}},o=(t,e)=>{for(const o in e.fields){const r=e.fields[o];r&&r.ref&&"reportValidity"in r.ref?s(r.ref,o,t):r.refs&&r.refs.forEach(e=>s(e,o,t))}},r=(s,r)=>{r.shouldUseNativeValidation&&o(s,r);const f={};for(const o in s){const n=t(r.fields,o),a=Object.assign(s[o]||{},{ref:n&&n.ref});if(i(r.names||Object.keys(s),o)){const s=Object.assign({},t(f,o));e(s,"root",a),e(f,o,s)}else e(f,o,a)}return f},i=(t,e)=>t.some(t=>t.startsWith(e+"."));export{r as toNestErrors,o as validateFieldsNatively};
//# sourceMappingURL=resolvers.mjs.map
