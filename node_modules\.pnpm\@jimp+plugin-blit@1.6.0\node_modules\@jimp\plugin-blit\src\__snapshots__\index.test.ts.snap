// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Blit over image > blit on middle, with no crop 1`] = `
Visualization:

▴▴▴▴▸▸▸▸
▴□□□□□□▸
▴□▥▥▥▥□▸
▴□▥■■▥□▸
▾□▥■■▥□◆
▾□▥▥▥▥□◆
▾□□□□□□◆
▾▾▾▾◆◆◆◆

Data:

FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-FF-FFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-FF-FFᶠᶠ BF-BF-BFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ 00-FF-00ᶠᶠ
00-00-FFᶠᶠ FF-FF-FFᶠᶠ BF-BF-BFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ FF-FF-FFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
`;

exports[`Blit over image > blit on middle, with x,y crop 1`] = `
Visualization:

▴▴▴▴▸▸▸▸
▴▴▴▴▸▸▸▸
▴▴▥▥▥▥□▸
▴▴▥■■▥□▸
▾▾▥■■▥□◆
▾▾▥▥▥▥□◆
▾▾□□□□□◆
▾▾▾▾◆◆◆◆

Data:

FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-00-00ᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-00-00ᶠᶠ BF-BF-BFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ 00-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ BF-BF-BFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
`;

exports[`Blit over image > blit on middle, with x,y,w,h crop 1`] = `
Visualization:

▴▴▴▴▸▸▸▸
▴▴▴▴▸▸▸▸
▴▴▥▥▥▥▸▸
▴▴▥■■▥▸▸
▾▾▥■■▥◆◆
▾▾▥▥▥▥◆◆
▾▾▾▾◆◆◆◆
▾▾▾▾◆◆◆◆

Data:

FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-00-00ᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-00-00ᶠᶠ BF-BF-BFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ BF-BF-BFᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ BF-BF-BFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ BF-BF-BFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
`;

exports[`Blit over image > blit on top, with no crop 1`] = `
Visualization:

□□□□□□▸▸
□▥▥▥▥□▸▸
□▥■■▥□▸▸
□▥■■▥□▸▸
□▥▥▥▥□◆◆
□□□□□□◆◆
▾▾▾▾◆◆◆◆
▾▾▾▾◆◆◆◆

Data:

FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-FF-FFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-FF-FFᶠᶠ BF-BF-BFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-FF-FFᶠᶠ BF-BF-BFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-FF-FFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
`;

exports[`Blit over image > blit partially out, on bottom-right 1`] = `
Visualization:

▴▴▴▴▸▸▸▸
▴▴▴▴▸▸▸▸
▴▴▴▴▸▸▸▸
▴▴▴□□□□□
▾▾▾□▥▥▥▥
▾▾▾□▥■■▥
▾▾▾□▥■■▥
▾▾▾□▥▥▥▥

Data:

FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-FFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-FFᶠᶠ BF-BF-BFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ BF-BF-BFᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-FFᶠᶠ BF-BF-BFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ BF-BF-BFᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-FFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
`;

exports[`Blit over image > blit partially out, on top-left 1`] = `
Visualization:

▥▥▥▥□▸▸▸
▥■■▥□▸▸▸
▥■■▥□▸▸▸
▥▥▥▥□▸▸▸
□□□□□◆◆◆
▾▾▾▾◆◆◆◆
▾▾▾▾◆◆◆◆
▾▾▾▾◆◆◆◆

Data:

BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
BF-BF-BFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
BF-BF-BFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
`;
