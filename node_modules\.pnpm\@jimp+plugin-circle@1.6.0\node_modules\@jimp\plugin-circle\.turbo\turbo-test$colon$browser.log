

> @jimp/plugin-circle@1.1.2 test:browser /Users/<USER>/Documents/jimp/plugins/plugin-circle
> vitest --config vitest.config.browser.mjs "--watch=false"

Re-optimizing dependencies because lockfile has changed
Port 5173 is in use, trying another one...
Port 5174 is in use, trying another one...
Port 5175 is in use, trying another one...
Port 5176 is in use, trying another one...

[7m[1m[36m RUN [39m[22m[27m [36mv2.0.5[39m [90m/Users/<USER>/Documents/jimp/plugins/plugin-circle[39m
[2m[32m      Browser runner started at http://localhost:5177/[39m[22m

[2m1:39:27 AM[22m [31m[1m[vite][22m[39m [31merror while updating dependencies:
Error: The service was stopped
    at /Users/<USER>/Documents/jimp/node_modules/.pnpm/esbuild@0.21.5/node_modules/esbuild/lib/main.js:993:26
    at responseCallbacks.<computed> (/Users/<USER>/Documents/jimp/node_modules/.pnpm/esbuild@0.21.5/node_modules/esbuild/lib/main.js:622:9)
    at Socket.afterClose (/Users/<USER>/Documents/jimp/node_modules/.pnpm/esbuild@0.21.5/node_modules/esbuild/lib/main.js:613:28)
    at Socket.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)[39m
[41m[30m ELIFECYCLE [39m[49m [31mCommand failed with exit code 130.[39m
