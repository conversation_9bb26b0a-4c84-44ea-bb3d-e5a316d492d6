{"version": 3, "sources": ["../src/index.js"], "names": ["fisheye", "options", "r", "cb", "source", "clone<PERSON>uiet", "bitmap", "width", "height", "scanQuiet", "x", "y", "hx", "hy", "Math", "sqrt", "pow", "rn", "cosA", "sinA", "newX", "round", "newY", "color", "getPixelColor", "setPixelColor", "call"], "mappings": ";;;;;;;AAAA;;AAEA;;;;;;eAMe;AAAA,SAAO;AACpBA,IAAAA,OADoB,qBACc;AAAA;;AAAA,UAA1BC,OAA0B,uEAAhB;AAAEC,QAAAA,CAAC,EAAE;AAAL,OAAgB;AAAA,UAAJC,EAAI;;AAChC,UAAI,OAAOF,OAAP,KAAmB,UAAvB,EAAmC;AACjCE,QAAAA,EAAE,GAAGF,OAAL;AACAA,QAAAA,OAAO,GAAG;AAAEC,UAAAA,CAAC,EAAE;AAAL,SAAV;AACD;;AAED,UAAME,MAAM,GAAG,KAAKC,UAAL,EAAf;AANgC,2BAOND,MAAM,CAACE,MAPD;AAAA,UAOxBC,KAPwB,kBAOxBA,KAPwB;AAAA,UAOjBC,MAPiB,kBAOjBA,MAPiB;AAShCJ,MAAAA,MAAM,CAACK,SAAP,CAAiB,CAAjB,EAAoB,CAApB,EAAuBF,KAAvB,EAA8BC,MAA9B,EAAsC,UAACE,CAAD,EAAIC,CAAJ,EAAU;AAC9C,YAAMC,EAAE,GAAGF,CAAC,GAAGH,KAAf;AACA,YAAMM,EAAE,GAAGF,CAAC,GAAGH,MAAf;AACA,YAAMN,CAAC,GAAGY,IAAI,CAACC,IAAL,CAAUD,IAAI,CAACE,GAAL,CAASJ,EAAE,GAAG,GAAd,EAAmB,CAAnB,IAAwBE,IAAI,CAACE,GAAL,CAASH,EAAE,GAAG,GAAd,EAAmB,CAAnB,CAAlC,CAAV;AACA,YAAMI,EAAE,GAAG,IAAIH,IAAI,CAACE,GAAL,CAASd,CAAT,EAAYD,OAAO,CAACC,CAApB,CAAf;AACA,YAAMgB,IAAI,GAAG,CAACN,EAAE,GAAG,GAAN,IAAaV,CAA1B;AACA,YAAMiB,IAAI,GAAG,CAACN,EAAE,GAAG,GAAN,IAAaX,CAA1B;AACA,YAAMkB,IAAI,GAAGN,IAAI,CAACO,KAAL,CAAW,CAACJ,EAAE,GAAGC,IAAL,GAAY,GAAb,IAAoBX,KAA/B,CAAb;AACA,YAAMe,IAAI,GAAGR,IAAI,CAACO,KAAL,CAAW,CAACJ,EAAE,GAAGE,IAAL,GAAY,GAAb,IAAoBX,MAA/B,CAAb;AACA,YAAMe,KAAK,GAAGnB,MAAM,CAACoB,aAAP,CAAqBJ,IAArB,EAA2BE,IAA3B,CAAd;;AAEA,QAAA,KAAI,CAACG,aAAL,CAAmBF,KAAnB,EAA0Bb,CAA1B,EAA6BC,CAA7B;AACD,OAZD;AAcA;;AACA,WAAKc,aAAL,CACErB,MAAM,CAACoB,aAAP,CAAqBjB,KAAK,GAAG,CAA7B,EAAgCC,MAAM,GAAG,CAAzC,CADF,EAEED,KAAK,GAAG,CAFV,EAGEC,MAAM,GAAG,CAHX;;AAMA,UAAI,0BAAcL,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACuB,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD;AApCmB,GAAP;AAAA,C", "sourcesContent": ["import { isNodePattern } from \"@jimp/utils\";\n\n/**\n * Creates a circle out of an image.\n * @param {object} options (optional) r: radius of effect\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {<PERSON><PERSON>} this for chaining of methods\n */\nexport default () => ({\n  fisheye(options = { r: 2.5 }, cb) {\n    if (typeof options === \"function\") {\n      cb = options;\n      options = { r: 2.5 };\n    }\n\n    const source = this.cloneQuiet();\n    const { width, height } = source.bitmap;\n\n    source.scanQuiet(0, 0, width, height, (x, y) => {\n      const hx = x / width;\n      const hy = y / height;\n      const r = Math.sqrt(Math.pow(hx - 0.5, 2) + Math.pow(hy - 0.5, 2));\n      const rn = 2 * Math.pow(r, options.r);\n      const cosA = (hx - 0.5) / r;\n      const sinA = (hy - 0.5) / r;\n      const newX = Math.round((rn * cosA + 0.5) * width);\n      const newY = Math.round((rn * sinA + 0.5) * height);\n      const color = source.getPixelColor(newX, newY);\n\n      this.setPixelColor(color, x, y);\n    });\n\n    /* Set center pixel color, otherwise it will be transparent */\n    this.setPixelColor(\n      source.getPixelColor(width / 2, height / 2),\n      width / 2,\n      height / 2\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "file": "index.js"}