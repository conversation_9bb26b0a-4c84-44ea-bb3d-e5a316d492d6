var e=require("@hookform/resolvers"),r=require("effect"),t=require("effect/ParseResult");exports.effectTsResolver=function(n,o){return void 0===o&&(o={errors:"all",onExcessProperty:"ignore"}),function(f,u,s){return t.decodeUnknown(n,o)(f).pipe(r.Effect.catchAll(function(e){return r.Effect.flip(t.ArrayFormatter.formatIssue(e))}),r.Effect.mapError(function(r){var t=r.reduce(function(e,r){return e[r.path.join(".")]={message:r.message,type:r._tag},e},{});return e.toNestErrors(t,s)}),r.Effect.tap(function(){return r.Effect.sync(function(){return s.shouldUseNativeValidation&&e.validateFieldsNatively({},s)})}),r.Effect.match({onFailure:function(e){return{errors:e,values:{}}},onSuccess:function(e){return{errors:{},values:e}}}),r.Effect.runPromise)}};
//# sourceMappingURL=effect-ts.js.map
