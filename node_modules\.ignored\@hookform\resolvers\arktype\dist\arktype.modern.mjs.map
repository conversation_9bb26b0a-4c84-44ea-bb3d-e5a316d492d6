{"version": 3, "file": "arktype.modern.mjs", "sources": ["../src/arktype.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { ArkErrors } from 'arktype';\nimport { FieldError, FieldErrors } from 'react-hook-form';\nimport type { Resolver } from './types';\n\nconst parseErrorSchema = (e: ArkErrors): Record<string, FieldError> => {\n  // copy code to type to match FieldError shape\n  e.forEach((e) => Object.assign(e, { type: e.code }));\n  // need to cast here because TS doesn't understand we added the type field\n  return e.byPath as never;\n};\n\nexport const arktypeResolver: Resolver =\n  (schema, _schemaOptions, resolverOptions = {}) =>\n  (values, _, options) => {\n    const out = schema(values);\n\n    if (out instanceof ArkErrors) {\n      return {\n        values: {},\n        errors: toNestErrors(parseErrorSchema(out), options),\n      };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return {\n      errors: {} as FieldErrors,\n      values: resolverOptions.raw ? values : out,\n    };\n  };\n"], "names": ["arktypeResolver", "schema", "_schemaOptions", "resolverOptions", "values", "_", "options", "out", "ArkErrors", "errors", "toNestErrors", "e", "for<PERSON>ach", "Object", "assign", "type", "code", "by<PERSON><PERSON>", "shouldUseNativeValidation", "validateFieldsNatively", "raw"], "mappings": "mHAKA,MAOaA,EACXA,CAACC,EAAQC,EAAgBC,EAAkB,CAAA,IAC3C,CAACC,EAAQC,EAAGC,KACV,MAAMC,EAAMN,EAAOG,GAEnB,OAAIG,aAAeC,EACV,CACLJ,OAAQ,CAAE,EACVK,OAAQC,GAfUC,EAeoBJ,EAb5CI,EAAEC,QAASD,GAAME,OAAOC,OAAOH,EAAG,CAAEI,KAAMJ,EAAEK,QAErCL,EAAEM,QAWyCX,KAIhDA,EAAQY,2BAA6BC,EAAuB,GAAIb,GAEzD,CACLG,OAAQ,CAAA,EACRL,OAAQD,EAAgBiB,IAAMhB,EAASG,IAvBnBI"}