!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("@hookform/resolvers"),require("effect"),require("effect/ParseResult")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers","effect","effect/ParseResult"],r):r((e||self).hookformResolversEffectTs={},e.hookformResolvers,e.Effect,e.EffectParseResult)}(this,function(e,r,t,o){e.effectTsResolver=function(e,f){return void 0===f&&(f={errors:"all",onExcessProperty:"ignore"}),function(n,s,u){return o.decodeUnknown(e,f)(n).pipe(t.Effect.catchAll(function(e){return t.Effect.flip(o.ArrayFormatter.formatIssue(e))}),t.Effect.mapError(function(e){var t=e.reduce(function(e,r){return e[r.path.join(".")]={message:r.message,type:r._tag},e},{});return r.toNestErrors(t,u)}),t.Effect.tap(function(){return t.Effect.sync(function(){return u.shouldUseNativeValidation&&r.validateFieldsNatively({},u)})}),t.Effect.match({onFailure:function(e){return{errors:e,values:{}}},onSuccess:function(e){return{errors:{},values:e}}}),t.Effect.runPromise)}}});
//# sourceMappingURL=effect-ts.umd.js.map
