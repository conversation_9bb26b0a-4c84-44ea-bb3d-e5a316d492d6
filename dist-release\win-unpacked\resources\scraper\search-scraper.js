"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const puppeteer_extra_1 = __importDefault(require("puppeteer-extra"));
const puppeteer_extra_plugin_stealth_1 = __importDefault(require("puppeteer-extra-plugin-stealth"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const jsdom_1 = require("jsdom");
// 强制设置控制台输出编码为UTF-8
if (process.platform === 'win32') {
    try {
        // 设置控制台代码页为UTF-8
        const { execSync } = require('child_process');
        execSync('chcp 65001', { stdio: 'inherit' });
        console.log('[Search Scraper] 已设置控制台代码页为UTF-8');
        // 强制设置Node.js进程编码
        process.env.LANG = 'zh_CN.UTF-8';
        process.env.LC_ALL = 'zh_CN.UTF-8';
        process.env.LC_CTYPE = 'zh_CN.UTF-8';
        // 设置stdout和stderr的编码
        if (process.stdout.setEncoding) {
            process.stdout.setEncoding('utf8');
        }
        if (process.stderr.setEncoding) {
            process.stderr.setEncoding('utf8');
        }
    }
    catch (err) {
        console.error('[Search Scraper] 设置控制台代码页失败:', err);
    }
}
else {
    // 非Windows平台也设置编码
    process.env.LANG = 'zh_CN.UTF-8';
    process.env.LC_ALL = 'zh_CN.UTF-8';
    process.env.LC_CTYPE = 'zh_CN.UTF-8';
}
console.log('[Search Scraper] === 开始执行搜索爬虫脚本 ===');
// 使用隐身插件
puppeteer_extra_1.default.use((0, puppeteer_extra_plugin_stealth_1.default)());
// 直接从HTML字符串中提取搜索结果数据
function extractSearchResultsFromHTML(html) {
    console.log('[Search Scraper] 开始从HTML字符串中提取搜索结果...');
    try {
        // 创建一个临时的DOM解析环境
        const dom = new jsdom_1.JSDOM(html, {
            pretendToBeVisual: false,
            runScripts: "outside-only"
        });
        const document = dom.window.document;
        // 查找所有文章元素
        let articles = Array.from(document.querySelectorAll('article'));
        console.log(`[Search Scraper] 找到 ${articles.length} 个搜索结果`);
        // 如果没有找到文章元素，尝试其他可能的选择器
        if (articles.length === 0) {
            console.log('[Search Scraper] 尝试使用备用选择器查找结果...');
            // 尝试其他可能的选择器
            const alternativeSelectors = [
                '.post',
                '.trainer-item',
                '.search-result',
                '.entry',
                '.post-item',
                '.grid-item'
            ];
            for (const selector of alternativeSelectors) {
                const elements = Array.from(document.querySelectorAll(selector));
                if (elements.length > 0) {
                    console.log(`[Search Scraper] 使用备用选择器 "${selector}" 找到 ${elements.length} 个结果`);
                    articles = elements;
                    break;
                }
            }
            // 如果仍然没有找到结果，尝试查找包含游戏名称的元素
            if (articles.length === 0) {
                console.log('[Search Scraper] 尝试查找包含游戏名称的元素...');
                const allElements = Array.from(document.querySelectorAll('*'));
                const potentialResults = allElements.filter(el => {
                    const text = el.textContent?.toLowerCase() || '';
                    return text.includes('trainer') && (text.includes('game version') || text.includes('options'));
                });
                if (potentialResults.length > 0) {
                    console.log(`[Search Scraper] 找到 ${potentialResults.length} 个可能的结果元素`);
                    articles = potentialResults;
                }
            }
            // 保存HTML用于调试
            const debugPath = path_1.default.join(__dirname, 'search_debug.html');
            fs_1.default.writeFileSync(debugPath, html);
            console.log(`[Search Scraper] 已保存HTML到 ${debugPath} 用于调试`);
        }
        const data = [];
        // 处理每个搜索结果
        for (const article of articles) {
            try {
                // 提取标题和链接
                let titleElement = article.querySelector('h2.post-title a') ||
                    article.querySelector('h2 a') ||
                    article.querySelector('.post-title a') ||
                    article.querySelector('a[href*="trainer"]');
                let title = titleElement?.textContent?.trim() || '';
                let link = titleElement?.getAttribute('href') || '';
                // 如果没有找到标题元素，尝试查找任何链接
                if (!title || !link) {
                    const allLinks = Array.from(article.querySelectorAll('a'));
                    for (const linkEl of allLinks) {
                        const href = linkEl.getAttribute('href') || '';
                        const text = linkEl.textContent?.trim() || '';
                        if (href.includes('trainer') && text.length > 5) {
                            title = text;
                            link = href;
                            break;
                        }
                    }
                }
                if (!title || !link) {
                    console.log('[Search Scraper] 跳过一个结果：标题或链接为空');
                    continue;
                }
                console.log(`[Search Scraper] 处理结果: ${title}`);
                // 提取图片URL
                const imageElement = article.querySelector('img.attachment-stylizer-small') ||
                    article.querySelector('img') ||
                    document.querySelector('img[src*="trainer"]');
                let imageUrl = imageElement?.getAttribute('src') || '';
                // 处理没有图片的情况
                if (!imageUrl) {
                    imageUrl = 'https://flingtrainer.com/wp-content/themes/stylizer/images/default-thumb.png';
                }
                // 提取游戏版本和更新日期
                const entryElement = article.querySelector('div.entry') || article;
                const metaText = entryElement?.textContent?.trim() || '';
                // 提取选项数量
                const optionsMatch = metaText.match(/(\d+)\s+Options/i);
                const options = optionsMatch ? parseInt(optionsMatch[1], 10) : 0;
                // 提取游戏版本
                const gameVersionMatch = metaText.match(/Game Version:\s*([^·]+?)(?:\s*·|$)/) ||
                    metaText.match(/Version:\s*([^·]+?)(?:\s*·|$)/);
                const gameVersion = gameVersionMatch ? gameVersionMatch[1].trim() : 'N/A';
                // 提取更新日期
                const lastUpdatedMatch = metaText.match(/Last Updated:\s*([\d.]+)/) ||
                    metaText.match(/Updated:\s*([\d.]+)/) ||
                    metaText.match(/(\d{1,2}\.\d{1,2}\.\d{2,4})/);
                const lastUpdated = lastUpdatedMatch ? lastUpdatedMatch[1].trim() : 'N/A';
                // 添加到结果数组
                data.push({
                    title,
                    link,
                    options,
                    gameVersion,
                    lastUpdated,
                    imageUrl
                });
            }
            catch (error) {
                console.error('[Search Scraper] 解析搜索结果时出错:', error);
            }
        }
        console.log(`[Search Scraper] 成功提取 ${data.length} 条搜索结果`);
        // 释放DOM内存
        dom.window.close();
        return data;
    }
    catch (error) {
        console.error('[Search Scraper] 提取搜索结果时出错:', error);
        return [];
    }
}
// 使用Puppeteer执行搜索
async function searchFlingTrainer(searchQuery) {
    console.log(`[Search Scraper] 开始搜索: "${searchQuery}"...`);
    let browser;
    try {
        // 优化浏览器启动选项
        browser = await puppeteer_extra_1.default.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--disable-gpu',
                '--disable-extensions',
                '--disable-component-extensions-with-background-pages',
                '--disable-default-apps',
                '--mute-audio',
                '--no-default-browser-check',
                '--no-first-run',
                '--js-flags=--max-old-space-size=512'
            ],
            ignoreDefaultArgs: ['--enable-automation'],
            defaultViewport: { width: 1024, height: 768 }
        });
        console.log('[Search Scraper] 浏览器启动成功');
        const page = await browser.newPage();
        console.log('[Search Scraper] 新页面创建成功');
        // 设置用户代理
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36');
        // 设置请求拦截
        await page.setRequestInterception(true);
        page.on('request', (request) => {
            const resourceType = request.resourceType();
            if (['image', 'font', 'stylesheet', 'media'].includes(resourceType)) {
                request.abort();
            }
            else {
                request.continue();
            }
        });
        // 设置超时
        page.setDefaultNavigationTimeout(30000);
        // 构造搜索URL
        const searchUrl = `https://flingtrainer.com/?s=${encodeURIComponent(searchQuery)}`;
        console.log(`[Search Scraper] 访问搜索URL: ${searchUrl}`);
        // 添加错误处理
        try {
            // 使用更可靠的导航方式
            const response = await page.goto(searchUrl, {
                waitUntil: 'networkidle2',
                timeout: 30000
            });
            if (!response) {
                throw new Error('没有收到页面响应');
            }
            const status = response.status();
            console.log(`[Search Scraper] 页面响应状态码: ${status}`);
            if (status >= 400) {
                throw new Error(`页面返回错误状态码: ${status}`);
            }
        }
        catch (navError) {
            console.error(`[Search Scraper] 导航到搜索页面失败: ${navError}`);
            // 尝试使用备用方法
            console.log('[Search Scraper] 尝试使用备用方法访问页面...');
            try {
                await page.goto('https://flingtrainer.com/', { waitUntil: 'networkidle2' });
                console.log('[Search Scraper] 成功访问主页，尝试使用搜索框');
                // 等待搜索框出现并点击
                await page.waitForSelector('.toggle-search', { timeout: 5000 });
                await page.click('.toggle-search');
                // 等待搜索输入框出现
                await page.waitForSelector('.search', { timeout: 5000 });
                // 输入搜索关键词并提交
                await page.type('.search', searchQuery);
                await page.keyboard.press('Enter');
                // 等待导航完成
                await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 10000 });
            }
            catch (altError) {
                console.error(`[Search Scraper] 备用导航方法也失败: ${altError}`);
                throw navError; // 仍然抛出原始错误
            }
        }
        // 等待页面加载
        try {
            // 先等待页面主体加载
            await page.waitForSelector('#content', { timeout: 10000 });
            console.log('[Search Scraper] 页面主体已加载');
            // 检查是否有搜索结果
            const hasResults = await page.evaluate(() => {
                return document.querySelectorAll('article').length > 0;
            });
            if (hasResults) {
                console.log('[Search Scraper] 找到搜索结果元素');
            }
            else {
                console.log('[Search Scraper] 未找到搜索结果元素，可能没有匹配的结果');
            }
        }
        catch (err) {
            console.log('[Search Scraper] 等待页面元素超时，继续尝试提取内容');
        }
        // 检查是否有广告弹窗并关闭
        try {
            const adCloseSelector = 'svg[viewBox="0 0 48 48"]';
            const hasAdPopup = await page.$(adCloseSelector);
            if (hasAdPopup) {
                console.log('[Search Scraper] 检测到广告弹窗，尝试关闭...');
                await page.click(adCloseSelector);
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }
        catch (error) {
            console.log('[Search Scraper] 处理广告弹窗时出错:', error);
        }
        // 获取页面HTML
        const html = await page.content();
        console.log(`[Search Scraper] 获取到HTML，长度: ${html.length}`);
        // 保存HTML用于调试
        const debugPath = path_1.default.join(__dirname, 'search_debug_full.html');
        fs_1.default.writeFileSync(debugPath, html);
        console.log(`[Search Scraper] 已保存完整HTML到 ${debugPath} 用于调试`);
        // 关闭浏览器
        await browser.close();
        console.log('[Search Scraper] 浏览器已关闭，开始解析搜索结果');
        // 解析搜索结果
        const searchResults = extractSearchResultsFromHTML(html);
        return searchResults;
    }
    catch (error) {
        console.error('[Search Scraper] 搜索过程中发生错误:', error);
        if (browser) {
            try {
                await browser.close();
            }
            catch (closeError) {
                console.error('[Search Scraper] 关闭浏览器时出错:', closeError);
            }
        }
        return [];
    }
}
// 主函数
async function main() {
    try {
        // 获取命令行参数中的搜索关键词
        const searchQuery = process.argv[2];
        if (!searchQuery) {
            console.error('[Search Scraper] 错误: 未提供搜索关键词');
            process.send({ success: false, error: '未提供搜索关键词' });
            process.exit(1);
        }
        // 确保中文编码正确
        console.log(`[Search Scraper] 原始搜索关键词: "${searchQuery}"`);
        console.log(`[Search Scraper] 编码: ${Buffer.from(searchQuery).toString('hex')}`);
        console.log(`[Search Scraper] 开始搜索关键词: "${searchQuery}"`);
        const searchResults = await searchFlingTrainer(searchQuery);
        if (searchResults.length > 0) {
            console.log(`[Search Scraper] 成功获取 ${searchResults.length} 条搜索结果`);
            console.log(`[Search Scraper] 第一条结果: ${JSON.stringify(searchResults[0])}`);
            // 通过IPC发送数据到主进程
            process.send({
                success: true,
                results: searchResults, // 使用results字段名
                query: searchQuery
            });
            console.log('[Search Scraper] 搜索结果已成功发送');
        }
        else {
            console.log('[Search Scraper] 未找到匹配的搜索结果');
            process.send({
                success: true,
                results: [],
                query: searchQuery
            });
        }
    }
    catch (error) {
        console.error('[Search Scraper] 执行过程中发生错误:', error);
        process.send({
            success: false,
            error: `执行过程中发生错误: ${error}`,
            query: process.argv[2] || ''
        });
    }
}
// 添加全局错误处理
process.on('uncaughtException', (error) => {
    console.error('[Search Scraper] 未捕获的异常:', error);
    process.send({
        success: false,
        error: `未捕获的异常: ${error}`,
        query: process.argv[2] || ''
    });
    process.exit(1);
});
process.on('unhandledRejection', (reason) => {
    console.error('[Search Scraper] 未处理的Promise拒绝:', reason);
});
// 设置超时
setTimeout(() => {
    console.error('[Search Scraper] 脚本执行超时，强制退出');
    process.send({
        success: false,
        error: '搜索超时，请稍后重试',
        query: process.argv[2] || ''
    });
    process.exit(1);
}, 60000); // 1分钟超时
// 执行主函数
main();
