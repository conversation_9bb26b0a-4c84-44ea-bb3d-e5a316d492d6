{"name": "is-fullwidth-code-point", "version": "3.0.0", "description": "Check if the character represented by a given Unicode code point is fullwidth", "license": "MIT", "repository": "sindresorhus/is-fullwidth-code-point", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "devDependencies": {"ava": "^1.3.1", "tsd-check": "^0.5.0", "xo": "^0.24.0"}}