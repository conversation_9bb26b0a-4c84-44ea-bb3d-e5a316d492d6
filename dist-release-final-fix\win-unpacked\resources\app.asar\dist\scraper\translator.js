"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const axios_1 = __importDefault(require("axios"));
const jsdom_1 = require("jsdom");
const https = __importStar(require("https"));
// 设置正确的编码，解决中文显示问题
process.env.LANG = 'zh_CN.UTF-8';
process.env.LC_ALL = 'zh_CN.UTF-8';
process.env.LC_CTYPE = 'zh_CN.UTF-8';
// 在Windows平台下，设置控制台代码页为UTF-8
if (process.platform === 'win32') {
    try {
        const { execSync } = require('child_process');
        execSync('chcp 65001', { stdio: 'ignore' });
    }
    catch (err) {
        console.error('设置控制台代码页失败:', err);
    }
}
// 确保可以正确处理中文字符
function ensureUtf8(str) {
    try {
        // 如果已经是有效的UTF-8字符串，直接返回
        if (Buffer.from(str).toString('utf8') === str) {
            return str;
        }
        // 尝试解码可能的非UTF-8编码
        const buffer = Buffer.from(str, 'binary');
        return buffer.toString('utf8');
    }
    catch (e) {
        console.error('字符串编码转换失败:', e);
        return str; // 失败时返回原始字符串
    }
}
console.log('--- 开始执行游戏名称翻译脚本 ---');
// 创建axios实例，配置超时和headers
const axiosInstance = axios_1.default.create({
    timeout: 15000,
    headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
    },
    httpsAgent: new https.Agent({
        rejectUnauthorized: false
    })
});
/**
 * 从百度搜索结果中提取游戏英文名称
 * @param chineseGameName 中文游戏名称
 * @returns 提取的英文名称或null
 */
async function translateGameName(chineseGameName) {
    try {
        console.log(`[翻译器] 开始翻译游戏名称: "${chineseGameName}"`);
        // 尝试多种搜索查询方式
        const searchQueries = [
            `${chineseGameName} 游戏 英文名`,
            `${chineseGameName} 英文名称`,
            `${chineseGameName} game english name`
        ];
        let englishName = null;
        let lastError = '';
        // 依次尝试不同的搜索查询
        for (const searchQuery of searchQueries) {
            if (englishName)
                break; // 如果已经找到英文名称，则跳出循环
            try {
                const encodedQuery = encodeURIComponent(searchQuery);
                const url = `https://www.baidu.com/s?wd=${encodedQuery}`;
                console.log(`[翻译器] 尝试搜索: "${searchQuery}", URL: ${url}`);
                // 发送请求获取搜索结果
                const response = await axiosInstance.get(url);
                if (response.status !== 200) {
                    lastError = `请求失败，状态码: ${response.status}`;
                    console.error(`[翻译器] ${lastError}`);
                    continue;
                }
                console.log(`[翻译器] 成功获取搜索结果，内容长度: ${response.data.length}`);
                // 使用JSDOM解析HTML
                const dom = new jsdom_1.JSDOM(response.data);
                const document = dom.window.document;
                // 提取可能包含英文名称的元素
                englishName = await extractEnglishNameFromBaidu(document, chineseGameName);
                if (englishName) {
                    console.log(`[翻译器] 成功提取英文名称: "${englishName}"`);
                    break;
                }
                else {
                    console.log(`[翻译器] 在查询"${searchQuery}"中未找到英文名称，尝试下一个查询`);
                }
            }
            catch (error) {
                lastError = error instanceof Error ? error.message : '搜索过程中出现未知错误';
                console.error(`[翻译器] 搜索"${searchQuery}"时出错:`, error);
            }
        }
        if (englishName) {
            return { success: true, englishName };
        }
        else {
            console.log('[翻译器] 所有尝试均未能提取英文名称');
            return { success: false, error: lastError || '未能从搜索结果中提取英文名称' };
        }
    }
    catch (error) {
        console.error('[翻译器] 翻译过程中出错:', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : '翻译过程中出现未知错误'
        };
    }
}
/**
 * 从百度搜索结果中提取英文游戏名称
 */
async function extractEnglishNameFromBaidu(document, chineseGameName) {
    try {
        console.log('[翻译器] 开始提取英文名称，添加2秒延迟等待页面内容完全加载...');
        // 添加2秒延迟，确保AI结果完全加载
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('[翻译器] 延迟结束，开始提取内容');
        // 尝试提取AI生成的回答内容（百度搜索可能会显示AI回答）
        const aiElements = document.querySelectorAll('.c-container.xpath-log, .result-op.c-container.xpath-log, .c-result.result, .c-container.new-pmd');
        for (const aiElement of aiElements) {
            // 检查是否是AI回答框
            if (aiElement.querySelector('.c-font-medium, .OP_LOG_LINK, .c-gap-bottom-small')) {
                const text = aiElement.textContent || '';
                // 使用更精确的模式匹配英文游戏名称
                const aiPatterns = [
                    new RegExp(`${chineseGameName}.*?(?:英文名称|英文名|英文|English name)[：:是为]\\s*[""]?([A-Za-z0-9\\s&:'\\.\\-]+)[""]?`, 'i'),
                    /(?:英文名称|英文名|英文|English name|英语名)[：:是为]\s*[""]?([A-Za-z0-9\s&:'\.\-]+)[""]?/i,
                    /[""]([A-Za-z][A-Za-z0-9\s&:'\.\-]{2,})[""].*?(?:中文名称|中文名|中文|Chinese name|中国語)/i
                ];
                for (const pattern of aiPatterns) {
                    const match = text.match(pattern);
                    if (match && match[1] && match[1].trim().length > 2) {
                        // 清理提取的文本
                        let englishName = match[1].trim()
                            .replace(/[\r\n]+/g, ' ')
                            .replace(/\s{2,}/g, ' ')
                            .replace(/[""]/g, '');
                        if (isValidGameName(englishName)) {
                            console.log(`[翻译器] 从AI回答中提取到英文名称: "${englishName}"`);
                            return englishName;
                        }
                    }
                }
            }
        }
        // 提取方法1: 从搜索结果摘要中提取
        const contentElements = document.querySelectorAll('.c-abstract, .content-right_8Zs40, .c-span9.c-span-last, .op_best_answer_question');
        for (let i = 0; i < contentElements.length; i++) {
            const text = contentElements[i].textContent || '';
            // 使用正则表达式匹配英文名称模式
            // 查找英文名称、英文名、英文、English name等关键词后面的英文内容
            const patterns = [
                /(?:英文名称|英文名|英文|English name|英语名)[：:]\s*([A-Za-z0-9\s&:'\-\.]+)/i,
                /([A-Za-z0-9\s&:'\-\.]{3,})\s*(?:中文名称|中文名|中文|Chinese name|中国語)[：:]/i,
                /[""]([A-Za-z][A-Za-z0-9\s&:'\-\.]{2,})[""](?!\s*[a-z]{2,})/i,
                /([A-Za-z][A-Za-z0-9\s&:'\-\.]{2,})/
            ];
            for (const pattern of patterns) {
                const match = text.match(pattern);
                if (match && match[1] && match[1].trim().length > 2) {
                    // 清理提取的文本
                    let englishName = match[1].trim()
                        .replace(/[\r\n]+/g, ' ')
                        .replace(/\s{2,}/g, ' ')
                        .replace(/[""]/g, '');
                    // 过滤掉一些常见的非游戏名词汇
                    if (isValidGameName(englishName)) {
                        console.log(`[翻译器] 从摘要中提取到英文名称: "${englishName}"`);
                        return englishName;
                    }
                }
            }
        }
        // 提取方法2: 从标题中提取
        const titleElements = document.querySelectorAll('.c-title, .t, .c-container .c-title-text');
        for (let i = 0; i < titleElements.length; i++) {
            const titleText = titleElements[i].textContent || '';
            // 在标题中查找可能的英文游戏名称
            const titlePatterns = [
                /[""]([A-Za-z][A-Za-z0-9\s&:'\-\.]{2,})[""](?!\s*[a-z]{2,})/i,
                /([A-Za-z][A-Za-z0-9\s&:'\-\.]{2,})/
            ];
            for (const pattern of titlePatterns) {
                const englishPart = titleText.match(pattern);
                if (englishPart && englishPart[1] && isValidGameName(englishPart[1])) {
                    const englishName = englishPart[1].trim().replace(/[""]/g, '');
                    console.log(`[翻译器] 从标题中提取到英文名称: "${englishName}"`);
                    return englishName;
                }
            }
        }
        // 提取方法3: 查找百科类结果
        const baikeElements = document.querySelectorAll('.c-container');
        for (let i = 0; i < baikeElements.length; i++) {
            const element = baikeElements[i];
            if (element.textContent?.includes('百科') || element.querySelector('.op-bk-polysemy-album')) {
                const text = element.textContent || '';
                // 百科中可能包含的模式
                const baikePatterns = [
                    /(?:英文名称|英文名|英文|English name|英语名)[：:]\s*([A-Za-z0-9\s&:'\-\.]+)/i,
                    /[""]([A-Za-z][A-Za-z0-9\s&:'\-\.]{2,})[""](?!\s*[a-z]{2,})/i,
                    /([A-Za-z][A-Za-z0-9\s&:'\-\.]{2,})/
                ];
                for (const pattern of baikePatterns) {
                    const englishPart = text.match(pattern);
                    if (englishPart && englishPart[1] && isValidGameName(englishPart[1])) {
                        const englishName = englishPart[1].trim().replace(/[""]/g, '');
                        console.log(`[翻译器] 从百科中提取到英文名称: "${englishName}"`);
                        return englishName;
                    }
                }
            }
        }
        // 提取方法4: 查找特定元素中的英文内容
        const specialElements = document.querySelectorAll('.c-gap-top-small, .c-row, .c-span-last');
        for (const element of specialElements) {
            const text = element.textContent || '';
            if (text.includes(chineseGameName) || text.includes('游戏') || text.includes('英文')) {
                const englishPart = text.match(/([A-Za-z][A-Za-z0-9\s&:'\-\.]{2,})/);
                if (englishPart && englishPart[1] && isValidGameName(englishPart[1])) {
                    const englishName = englishPart[1].trim();
                    console.log(`[翻译器] 从特定元素中提取到英文名称: "${englishName}"`);
                    return englishName;
                }
            }
        }
        console.log('[翻译器] 未能从搜索结果中提取英文名称');
        return null;
    }
    catch (error) {
        console.error('[翻译器] 提取英文名称时出错:', error);
        return null;
    }
}
/**
 * 检查提取的文本是否可能是有效的游戏名称
 */
function isValidGameName(text) {
    // 排除常见的非游戏名词汇
    const invalidTerms = [
        'download', 'official', 'website', 'wiki', 'wikipedia', 'baidu',
        'steam', 'epic', 'game', 'games', 'gaming', 'gamer', 'gameplay',
        'trailer', 'review', 'reviews', 'walkthrough', 'cheats', 'mods',
        'system', 'requirements', 'minimum', 'recommended', 'settings',
        'graphics', 'resolution', 'fps', 'performance', 'benchmark',
        'installation', 'update', 'patch', 'version', 'release', 'date',
        'english', 'chinese', 'name', 'title', 'translation'
    ];
    // 如果文本完全匹配任何一个无效词汇，则返回false
    if (invalidTerms.some(term => text.toLowerCase() === term)) {
        return false;
    }
    // 文本长度应该在合理范围内
    if (text.length < 3 || text.length > 50) {
        return false;
    }
    // 应该包含至少一个字母
    if (!/[a-zA-Z]/.test(text)) {
        return false;
    }
    // 排除纯数字和符号组合
    if (!/[a-zA-Z]{2,}/.test(text)) {
        return false;
    }
    // 排除可能是网址的文本
    if (/^www\./.test(text) || /\.com$/.test(text) || /http/.test(text)) {
        return false;
    }
    return true;
}
// 主函数 - 处理命令行参数
async function main() {
    const gameName = process.argv[2];
    if (!gameName) {
        const result = { success: false, error: '未提供游戏名称' };
        if (process.send) {
            process.send(result);
        }
        else {
            console.log('RESULT:' + JSON.stringify(result));
        }
        process.exit(1);
    }
    try {
        const result = await translateGameName(gameName);
        // 将结果发送回主进程
        if (process.send) {
            process.send(result);
        }
        else {
            console.log('RESULT:' + JSON.stringify(result));
        }
    }
    catch (error) {
        console.error('[翻译器] 处理消息时出错:', error);
        const result = {
            success: false,
            error: error instanceof Error ? error.message : '翻译过程中出现未知错误'
        };
        if (process.send) {
            process.send(result);
        }
        else {
            console.log('RESULT:' + JSON.stringify(result));
        }
    }
}
// 监听主进程消息（兼容旧模式）
process.on('message', async (message) => {
    if (!message || !message.gameName) {
        const result = { success: false, error: '未提供游戏名称' };
        if (process.send) {
            process.send(result);
        }
        else {
            console.log('RESULT:' + JSON.stringify(result));
        }
        return;
    }
    try {
        const result = await translateGameName(message.gameName);
        // 将结果发送回主进程
        if (process.send) {
            process.send(result);
        }
        else {
            console.log('RESULT:' + JSON.stringify(result));
        }
    }
    catch (error) {
        console.error('[翻译器] 处理消息时出错:', error);
        const result = {
            success: false,
            error: error instanceof Error ? error.message : '翻译过程中出现未知错误'
        };
        if (process.send) {
            process.send(result);
        }
        else {
            console.log('RESULT:' + JSON.stringify(result));
        }
    }
});
// 处理进程错误
process.on('uncaughtException', (error) => {
    console.error('[翻译器] 未捕获的异常:', error);
    const result = {
        success: false,
        error: error instanceof Error ? error.message : '翻译过程中出现未捕获的异常'
    };
    if (process.send) {
        process.send(result);
    }
    else {
        console.log('RESULT:' + JSON.stringify(result));
    }
    // 给主进程一些时间来接收消息，然后退出
    setTimeout(() => process.exit(1), 1000);
});
// 添加全局错误处理
process.on('unhandledRejection', (reason) => {
    console.error('[翻译脚本] 未处理的Promise拒绝:', reason);
});
// 设置超时，避免进程挂起
setTimeout(() => {
    console.error('[翻译脚本] 脚本执行超时，强制退出');
    process.exit(1);
}, 30000); // 30秒超时
// 如果有命令行参数，直接执行主函数
if (process.argv[2]) {
    main().catch(error => {
        console.error('[翻译器] 主函数执行失败:', error);
        process.exit(1);
    });
}
