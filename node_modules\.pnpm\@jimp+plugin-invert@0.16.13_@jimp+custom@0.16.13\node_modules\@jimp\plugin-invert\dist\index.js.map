{"version": 3, "sources": ["../src/index.js"], "names": ["invert", "cb", "scanQuiet", "bitmap", "width", "height", "x", "y", "idx", "data", "call"], "mappings": ";;;;;;;AAAA;;AAEA;;;;;eAKe;AAAA,SAAO;AACpBA,IAAAA,MADoB,kBACbC,EADa,EACT;AACT,WAAKC,SAAL,CACE,CADF,EAEE,CAFF,EAGE,KAAKC,MAAL,CAAYC,KAHd,EAIE,KAAKD,MAAL,CAAYE,MAJd,EAKE,UAAUC,CAAV,EAAaC,CAAb,EAAgBC,GAAhB,EAAqB;AACnB,aAAKL,MAAL,CAAYM,IAAZ,CAAiBD,GAAjB,IAAwB,MAAM,KAAKL,MAAL,CAAYM,IAAZ,CAAiBD,GAAjB,CAA9B;AACA,aAAKL,MAAL,CAAYM,IAAZ,CAAiBD,GAAG,GAAG,CAAvB,IAA4B,MAAM,KAAKL,MAAL,CAAYM,IAAZ,CAAiBD,GAAG,GAAG,CAAvB,CAAlC;AACA,aAAKL,MAAL,CAAYM,IAAZ,CAAiBD,GAAG,GAAG,CAAvB,IAA4B,MAAM,KAAKL,MAAL,CAAYM,IAAZ,CAAiBD,GAAG,GAAG,CAAvB,CAAlC;AACD,OATH;;AAYA,UAAI,0BAAcP,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACS,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD;AAnBmB,GAAP;AAAA,C", "sourcesContent": ["import { isNodePattern } from \"@jimp/utils\";\n\n/**\n * Inverts the image\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {<PERSON><PERSON>} this for chaining of methods\n */\nexport default () => ({\n  invert(cb) {\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        this.bitmap.data[idx] = 255 - this.bitmap.data[idx];\n        this.bitmap.data[idx + 1] = 255 - this.bitmap.data[idx + 1];\n        this.bitmap.data[idx + 2] = 255 - this.bitmap.data[idx + 2];\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "file": "index.js"}