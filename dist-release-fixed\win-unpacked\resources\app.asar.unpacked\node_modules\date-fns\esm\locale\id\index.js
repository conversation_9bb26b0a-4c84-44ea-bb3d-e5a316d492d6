import formatDistance from "./_lib/formatDistance/index.js";
import formatLong from "./_lib/formatLong/index.js";
import formatRelative from "./_lib/formatRelative/index.js";
import localize from "./_lib/localize/index.js";
import match from "./_lib/match/index.js";
/**
 * @type {Locale}
 * @category Locales
 * @summary Indonesian locale.
 * @language Indonesian
 * @iso-639-2 ind
 * <AUTHOR> [@rbudiharso]{@link https://github.com/rbudiharso}
 * <AUTHOR> Nata [@bentinata]{@link https://github.com/bentinata}
 * <AUTHOR> [@deerawan]{@link https://github.com/deerawan}
 * <AUTHOR> Ajitiono [@imballinst]{@link https://github.com/imballinst}
 */
var locale = {
  code: 'id',
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1
  }
};
export default locale;