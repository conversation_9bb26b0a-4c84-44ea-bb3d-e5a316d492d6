// 主进程中的爬虫工作器
// 直接在主进程中运行，避免子进程模块依赖问题

import puppeteer from 'puppeteer';
import { JSDOM } from 'jsdom';
import { <PERSON>, Browser } from 'puppeteer';
import fs from 'fs';
import path from 'path';

interface TrainerData {
  title: string;
  link: string;
  description: string;
  downloadLink: string;
  imageUrl: string;
  tags: string[];
  lastUpdated: string;  // 改名为 lastUpdated
  gameVersion: string;  // 改名为 gameVersion
  fileSize: string;
  downloads: string;
  options: number;      // 添加 options 字段
}

interface ScrapingResult {
  success: boolean;
  data?: TrainerData[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  error?: string;
}

export class ScraperWorker {
  private browser: Browser | null = null;
  private chromiumPath: string;

  constructor(chromiumPath: string) {
    this.chromiumPath = chromiumPath;
  }

  async initialize(): Promise<void> {
    try {
      console.log('[ScraperWorker] 初始化浏览器...');
      console.log(`[ScraperWorker] 使用 Chromium 路径: ${this.chromiumPath}`);

      this.browser = await puppeteer.launch({
        headless: true,
        executablePath: this.chromiumPath,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor',
          '--lang=zh-CN'
        ],
        defaultViewport: {
          width: 1920,
          height: 1080
        }
      });

      console.log('[ScraperWorker] ✅ 浏览器初始化成功');
    } catch (error) {
      console.error('[ScraperWorker] ❌ 浏览器初始化失败:', error);
      throw error;
    }
  }

  async scrapeTrainers(page: number = 1): Promise<ScrapingResult> {
    if (!this.browser) {
      throw new Error('浏览器未初始化');
    }

    let browserPage: Page | null = null;

    try {
      console.log(`[ScraperWorker] 开始抓取第 ${page} 页数据...`);

      // 创建新页面
      browserPage = await this.browser.newPage();
      
      // 设置用户代理
      await browserPage.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
      
      // 设置额外的请求头
      await browserPage.setExtraHTTPHeaders({
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8'
      });

      // 构建URL
      const baseUrl = 'https://flingtrainer.com';
      const url = page > 1 ? `${baseUrl}/page/${page}/` : baseUrl;
      
      console.log(`[ScraperWorker] 访问网站: ${url}`);

      // 访问页面
      await browserPage.goto(url, {
        waitUntil: 'networkidle2',
        timeout: 30000
      });

      console.log('[ScraperWorker] 页面加载完成，获取HTML内容...');

      // 获取页面HTML
      const htmlContent = await browserPage.content();
      
      // 检查分页信息
      const paginationInfo = await this.checkPagination(browserPage);
      
      console.log(`[ScraperWorker] 分页检查结果 - 上一页: ${paginationInfo.hasPrevPage}, 下一页: ${paginationInfo.hasNextPage}`);

      // 关闭页面
      await browserPage.close();
      browserPage = null;

      console.log('[ScraperWorker] 页面已关闭，开始解析数据');

      // 解析HTML数据
      const trainers = this.parseTrainersFromHTML(htmlContent);

      console.log(`[ScraperWorker] 从HTML中成功提取 ${trainers.length} 条数据`);

      return {
        success: true,
        data: trainers,
        pagination: {
          currentPage: page,
          totalPages: 10, // 假设总页数，实际可以从页面解析
          hasNextPage: paginationInfo.hasNextPage,
          hasPrevPage: paginationInfo.hasPrevPage
        }
      };

    } catch (error) {
      console.error('[ScraperWorker] ❌ 抓取过程中发生错误:', error);
      
      if (browserPage) {
        try {
          await browserPage.close();
        } catch (closeError) {
          console.error('[ScraperWorker] 关闭页面时发生错误:', closeError);
        }
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private async checkPagination(page: Page): Promise<{hasNextPage: boolean, hasPrevPage: boolean}> {
    try {
      const paginationInfo = await page.evaluate(() => {
        // 查找分页导航
        const paginationContainer = document.querySelector('.pagination, .nav-links, .page-numbers');
        if (!paginationContainer) {
          return { hasNextPage: false, hasPrevPage: false };
        }

        // 检查是否有下一页
        const nextLink = paginationContainer.querySelector('a[rel="next"], .next, .page-numbers.next');
        const hasNextPage = !!nextLink;

        // 检查是否有上一页
        const prevLink = paginationContainer.querySelector('a[rel="prev"], .prev, .page-numbers.prev');
        const hasPrevPage = !!prevLink;

        return { hasNextPage, hasPrevPage };
      });

      return paginationInfo;
    } catch (error) {
      console.error('[ScraperWorker] 检查分页信息时发生错误:', error);
      return { hasNextPage: false, hasPrevPage: false };
    }
  }

  private parseTrainersFromHTML(htmlContent: string): TrainerData[] {
    try {
      console.log('[ScraperWorker] 开始从HTML字符串中提取数据...');
      
      const dom = new JSDOM(htmlContent);
      const document = dom.window.document;

      // 查找文章容器
      const articles = document.querySelectorAll('article, .post, .entry, .trainer-item');
      console.log(`[ScraperWorker] 找到 ${articles.length} 个文章元素`);

      const trainers: TrainerData[] = [];

      articles.forEach((article, index) => {
        try {
          // 提取标题和链接
          const titleElement = article.querySelector('h1 a, h2 a, h3 a, .entry-title a, .post-title a');
          const title = titleElement?.textContent?.trim() || '';
          const link = titleElement?.getAttribute('href') || '';

          if (!title || !link) {
            return; // 跳过无效数据
          }

          // 提取描述
          const descElement = article.querySelector('.entry-content, .post-content, .excerpt, p');
          const description = descElement?.textContent?.trim().substring(0, 200) || '';

          // 提取图片
          const imgElement = article.querySelector('img');
          const imageUrl = imgElement?.getAttribute('src') || imgElement?.getAttribute('data-src') || '';

          // 提取其他信息
          const lastUpdated = this.extractUpdateTime(article);
          const gameVersion = this.extractVersion(article);
          const tags = this.extractTags(article);
          const options = this.extractOptions(article);

          const trainer: TrainerData = {
            title,
            link: link.startsWith('http') ? link : `https://flingtrainer.com${link}`,
            description,
            downloadLink: '', // 需要进入详情页获取
            imageUrl: imageUrl.startsWith('http') ? imageUrl : `https://flingtrainer.com${imageUrl}`,
            tags,
            lastUpdated,
            gameVersion,
            fileSize: '',
            downloads: '',
            options
          };



          trainers.push(trainer);
        } catch (error) {
          console.error(`[ScraperWorker] 解析第 ${index + 1} 个文章时发生错误:`, error);
        }
      });

      return trainers;
    } catch (error) {
      console.error('[ScraperWorker] 解析HTML时发生错误:', error);
      return [];
    }
  }

  private extractUpdateTime(article: Element): string {
    try {
      // 方法1: 从 entry 内容中提取 "Last Updated" 信息
      const entryElement = article.querySelector('.entry');
      if (entryElement) {
        const entryText = entryElement.textContent || '';
        const lastUpdatedMatch = entryText.match(/Last Updated:\s*(\d{4}\.\d{2}\.\d{2})/i);
        if (lastUpdatedMatch) {
          return lastUpdatedMatch[1];
        }
      }

      // 方法2: 从日期详情中组合日期
      const dayElement = article.querySelector('.post-details-day');
      const monthElement = article.querySelector('.post-details-month');
      const yearElement = article.querySelector('.post-details-year');

      if (dayElement && monthElement && yearElement) {
        const day = dayElement.textContent?.trim();
        const month = monthElement.textContent?.trim();
        const year = yearElement.textContent?.trim();

        if (day && month && year) {
          return `${year}.${this.monthToNumber(month)}.${day.padStart(2, '0')}`;
        }
      }

      // 方法3: 传统选择器
      const timeSelectors = ['.post-date', '.entry-date', '.published', 'time', '.date'];
      for (const selector of timeSelectors) {
        const timeElement = article.querySelector(selector);
        if (timeElement) {
          const timeText = timeElement.textContent?.trim() || timeElement.getAttribute('datetime') || '';
          if (timeText) return timeText;
        }
      }

    } catch (error) {
      console.error('[ScraperWorker] 提取时间时发生错误:', error);
    }

    return new Date().toLocaleDateString();
  }

  private monthToNumber(monthName: string): string {
    const months: { [key: string]: string } = {
      'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
      'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
      'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
    };
    return months[monthName] || '01';
  }

  private extractVersion(article: Element): string {
    try {
      // 从 entry 内容中提取版本信息
      const entryElement = article.querySelector('.entry');
      if (entryElement) {
        const entryText = entryElement.textContent || '';

        // 匹配 "Game Version: v1.0-v20250701+" 格式
        const gameVersionMatch = entryText.match(/Game Version:\s*(v?[\d\.\-\w\+]+)/i);
        if (gameVersionMatch) {
          return gameVersionMatch[1];
        }

        // 匹配其他版本格式
        const versionMatch = entryText.match(/Version:\s*(v?[\d\.\-\w\+]+)/i);
        if (versionMatch) {
          return versionMatch[1];
        }

        // 匹配 "v1.0" 等简单格式
        const simpleVersionMatch = entryText.match(/\b(v[\d\.\-\w\+]+)\b/i);
        if (simpleVersionMatch) {
          return simpleVersionMatch[1];
        }
      }

    } catch (error) {
      console.error('[ScraperWorker] 提取版本时发生错误:', error);
    }

    return '';
  }

  private extractOptions(article: Element): number {
    try {
      // 从 entry 内容中提取选项数量
      const entryElement = article.querySelector('.entry');
      if (entryElement) {
        const entryText = entryElement.textContent || '';

        // 匹配 "61 Options" 格式
        const optionsMatch = entryText.match(/(\d+)\s+Options/i);
        if (optionsMatch) {
          return parseInt(optionsMatch[1], 10);
        }
      }

    } catch (error) {
      console.error('[ScraperWorker] 提取选项数量时发生错误:', error);
    }

    return 0;
  }

  private extractTags(article: Element): string[] {
    const tags: string[] = [];
    const tagElements = article.querySelectorAll('.tag, .category, .post-tag, .entry-tag');
    
    tagElements.forEach(tag => {
      const tagText = tag.textContent?.trim();
      if (tagText) tags.push(tagText);
    });
    
    return tags;
  }

  async close(): Promise<void> {
    if (this.browser) {
      try {
        await this.browser.close();
        console.log('[ScraperWorker] ✅ 浏览器已关闭');
      } catch (error) {
        console.error('[ScraperWorker] 关闭浏览器时发生错误:', error);
      }
      this.browser = null;
    }
  }
}
