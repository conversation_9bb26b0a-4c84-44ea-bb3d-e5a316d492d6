{"version": 3, "file": "mask-color.js", "sourceRoot": "", "sources": ["../../src/mask-color.ts"], "names": [], "mappings": "AAAA,iBAAiB;AACjB,EAAE;AACF,qCAAqC;AACrC,qCAAqC;AACrC,qCAAqC;AACrC,EAAE;AACF,8DAA8D;AAC9D,EAAE;AACF,0CAA0C;AAC1C,EAAE;AACF,sBAAsB;AACtB,UAAU;AACV,EAAE;AACF,WAAW;AACX,EAAE;AACF,2CAA2C;AAC3C,2CAA2C;AAC3C,2CAA2C;AAC3C,EAAE;AACF,oEAAoE;AACpE,EAAE;AACF,sBAAsB;AACtB,gBAAgB;AAChB,EAAE;AACF,WAAW;AACX,EAAE;AACF,2CAA2C;AAC3C,2CAA2C;AAC3C,EAAE;AACF,6DAA6D;AAC7D,EAAE;AACF,gDAAgD;AAChD,gDAAgD;AAChD,gDAAgD;AAChD,gDAAgD;AAChD,6CAA6C;AAC7C,EAAE;AACF,8EAA8E;AAC9E,gDAAgD;AAEhD,MAAM,CAAC,OAAO,UAAU,SAAS,CAC/B,OAAe,EACf,SAAiB,EACjB,QAAgB,EAChB,SAAiB;IAEjB,MAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;IAC1C,MAAM,UAAU,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC;IAChD,MAAM,SAAS,GAAG,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;IAC7C,MAAM,UAAU,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC;IAEhD,MAAM,eAAe,GAAG,OAAO,GAAG,QAAQ,GAAG,CAAC,CAAC;IAC/C,MAAM,iBAAiB,GAAG,SAAS,GAAG,UAAU,GAAG,CAAC,CAAC;IACrD,MAAM,gBAAgB,GAAG,QAAQ,GAAG,SAAS,GAAG,CAAC,CAAC;IAClD,MAAM,iBAAiB,GAAG,SAAS,GAAG,UAAU,GAAG,CAAC,CAAC;IAErD,OAAO;QACL,QAAQ,EAAE,CAAC,CAAS,EAAE,EAAE,CACtB,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,GAAG,eAAe;QACxD,UAAU,EAAE,CAAC,CAAS,EAAE,EAAE,CACxB,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,UAAU,CAAC,GAAG,KAAK,CAAC,GAAG,iBAAiB;QAC9D,SAAS,EAAE,CAAC,CAAS,EAAE,EAAE,CACvB,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,SAAS,CAAC,GAAG,KAAK,CAAC,GAAG,gBAAgB;QAC3D,UAAU,EACR,SAAS,KAAK,CAAC;YACb,CAAC,CAAC,CAAC,CAAS,EAAE,EAAE,CACZ,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,UAAU,CAAC,GAAG,KAAK,CAAC,GAAG,iBAAiB;YAChE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;KAChB,CAAC;AACJ,CAAC"}