{"version": 3, "sources": ["../src/index.ts", "../src/use-rect.tsx"], "sourcesContent": ["export { useRect } from './use-rect';\n", "import * as React from 'react';\nimport { observeElementRect } from '@radix-ui/rect';\n\nimport type { Measurable } from '@radix-ui/rect';\n\n/**\n * Use this custom hook to get access to an element's rect (getBoundingClientRect)\n * and observe it along time.\n */\nfunction useRect(measurable: Measurable | null) {\n  const [rect, setRect] = React.useState<DOMRect>();\n  React.useEffect(() => {\n    if (measurable) {\n      const unobserve = observeElementRect(measurable, setRect);\n      return () => {\n        setRect(undefined);\n        unobserve();\n      };\n    }\n    return;\n  }, [measurable]);\n  return rect;\n}\n\nexport { useRect };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,YAAuB;AACvB,kBAAmC;AAQnC,SAAS,QAAQ,YAA+B;AAC9C,QAAM,CAAC,MAAM,OAAO,IAAU,eAAkB;AAChD,EAAM,gBAAU,MAAM;AACpB,QAAI,YAAY;AACd,YAAM,gBAAY,gCAAmB,YAAY,OAAO;AACxD,aAAO,MAAM;AACX,gBAAQ,MAAS;AACjB,kBAAU;AAAA,MACZ;AAAA,IACF;AACA;AAAA,EACF,GAAG,CAAC,UAAU,CAAC;AACf,SAAO;AACT;", "names": []}