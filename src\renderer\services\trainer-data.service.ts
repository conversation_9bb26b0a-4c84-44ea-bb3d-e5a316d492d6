import { useState, useEffect } from 'react';

// 定义下载选项的类型
export interface DownloadOption {
  fileName: string;
  downloadUrl: string;
  dateAdded: string;
  fileSize: string;
  downloads: number;
}

// 定义爬取数据的类型
export interface TrainerData {
  title: string;
  link: string;
  description: string;
  downloadLink: string;
  imageUrl: string;
  tags: string[];
  lastUpdated: string;
  gameVersion: string;
  fileSize: string;
  downloads: string;
  options: number;
  // 新增字段，用于存储详情页的下载选项
  downloadOptions?: DownloadOption[];
}

// 定义分页信息接口
export interface PaginationInfo {
  currentPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  totalPages: number;
}

// 定义IPC渲染器接口
interface IpcRenderer {
  on: (channel: string, func: (...args: any[]) => void) => void;
  send: (channel: string, ...args: any[]) => void;
  invoke: (channel: string, ...args: any[]) => Promise<any>;
  removeListener: (channel: string, func: (...args: any[]) => void) => void;
}

// 创建一个自定义钩子来管理修改器数据
export function useTrainerData() {
  const [trainerData, setTrainerData] = useState<TrainerData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [lastUpdated, setLastUpdated] = useState<string>('正在同步...');
  const [error, setError] = useState<string | null>(null);
  // 添加分页相关状态
  const [pagination, setPagination] = useState<PaginationInfo>({
    currentPage: 1,
    hasNextPage: false,
    hasPrevPage: false,
    totalPages: 1
  });

  // 手动触发数据刷新
  const refreshData = (page: number = 1) => {
    setIsLoading(true);
    setError(null);
    setLastUpdated('正在同步...');
    
    // 向主进程发送刷新数据请求，包含页码信息
    try {
      const ipcRenderer = (window as any).electron?.ipcRenderer as IpcRenderer;
      if (ipcRenderer && ipcRenderer.send) {
        ipcRenderer.send('request-trainer-data', page);
        console.log(`请求刷新修改器数据，页码: ${page}`);
      } else {
        throw new Error('IPC通信不可用');
      }
    } catch (err) {
      console.error('无法发送刷新请求:', err);
      setError('IPC通信错误');
      setIsLoading(false);
    }
  };

  // 切换页面
  const changePage = (page: number) => {
    if (page < 1) return;
    refreshData(page);
  };

  // 下一页
  const nextPage = () => {
    if (pagination.hasNextPage) {
      changePage(pagination.currentPage + 1);
    }
  };

  // 上一页
  const prevPage = () => {
    if (pagination.hasPrevPage) {
      changePage(pagination.currentPage - 1);
    }
  };

  useEffect(() => {
    try {
      const ipcRenderer = (window as any).electron?.ipcRenderer as IpcRenderer;
      if (!ipcRenderer || !ipcRenderer.on) {
        throw new Error('IPC通信不可用');
      }

      // 添加一个调试日志
      console.log('开始设置IPC监听器...');

      // 监听来自主进程的数据更新
      ipcRenderer.on('update-trainer-data', (data: any, paginationData: PaginationInfo) => {
        console.log('收到新的修改器数据:', data ? (Array.isArray(data) ? data.length : 'non-array') : 'null', '条');
        console.log('数据类型:', data ? (Array.isArray(data) ? 'Array' : typeof data) : 'null');
        console.log('分页信息:', paginationData);
        
        if (Array.isArray(data) && data.length > 0) {
          console.log('第一条数据示例:', JSON.stringify(data[0]).substring(0, 100) + '...');
        }
        
        // 检查是否有活跃的搜索结果，如果有则不更新trainerData
        const hasActiveSearchResults = localStorage.getItem('lastSearchResults') !== null;
        if (!hasActiveSearchResults) {
          setTrainerData(data as TrainerData[]);
        } else {
          console.log('存在活跃的搜索结果，不更新trainerData');
        }
        
        setIsLoading(false);
        setLastUpdated(new Date().toLocaleTimeString());
        setError(null);
        
        // 更新分页信息
        if (paginationData) {
          setPagination(paginationData);
        }
      });

      // 监听错误消息
      ipcRenderer.on('update-trainer-data-error', (errorMsg: any) => {
        console.error('加载修改器数据出错:', errorMsg);
        setError(errorMsg as string);
        setIsLoading(false);
        setLastUpdated('加载失败');
      });

      // 组件挂载时，通知主进程已准备好接收数据
      try {
        ipcRenderer.send('renderer-ready');
        console.log('渲染进程已就绪，发送 "renderer-ready" 信号 - 时间:', new Date().toLocaleTimeString());
      } catch (err) {
        console.error('发送renderer-ready信号失败:', err);
      }

    } catch (err) {
      console.error('设置IPC监听器失败:', err);
      setError('IPC通信错误');
      setIsLoading(false);
    }

    return () => {
      // 清理函数（暂时不需要，因为我们没有方法移除 IPC 监听器）
    };
  }, []); // 空依赖数组确保只在组件挂载时注册一次监听器

  return {
    trainerData,
    isLoading,
    lastUpdated,
    error,
    refreshData,
    pagination,
    nextPage,
    prevPage,
    changePage
  };
} 