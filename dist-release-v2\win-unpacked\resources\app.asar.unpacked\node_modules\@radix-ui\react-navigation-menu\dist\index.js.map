{"version": 3, "sources": ["../src/index.ts", "../src/navigation-menu.tsx"], "sourcesContent": ["'use client';\nexport {\n  createNavigationMenuScope,\n  //\n  NavigationMenu,\n  NavigationMenuSub,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuContent,\n  NavigationMenuViewport,\n  //\n  Root,\n  Sub,\n  List,\n  Item,\n  Trigger,\n  Link,\n  Indicator,\n  Content,\n  Viewport,\n} from './navigation-menu';\nexport type {\n  NavigationMenuProps,\n  NavigationMenuSubProps,\n  NavigationMenuListProps,\n  NavigationMenuItemProps,\n  NavigationMenuTriggerProps,\n  NavigationMenuLinkProps,\n  NavigationMenuIndicatorProps,\n  NavigationMenuContentProps,\n  NavigationMenuViewportProps,\n} from './navigation-menu';\n", "import * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { composeRefs, useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { Presence } from '@radix-ui/react-presence';\nimport { useId } from '@radix-ui/react-id';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport * as VisuallyHiddenPrimitive from '@radix-ui/react-visually-hidden';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Orientation = 'vertical' | 'horizontal';\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenu\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAVIGATION_MENU_NAME = 'NavigationMenu';\n\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  NavigationMenuTriggerElement,\n  { value: string }\n>(NAVIGATION_MENU_NAME);\n\nconst [FocusGroupCollection, useFocusGroupCollection, createFocusGroupCollectionScope] =\n  createCollection<FocusGroupItemElement, {}>(NAVIGATION_MENU_NAME);\n\ntype ScopedProps<P> = P & { __scopeNavigationMenu?: Scope };\nconst [createNavigationMenuContext, createNavigationMenuScope] = createContextScope(\n  NAVIGATION_MENU_NAME,\n  [createCollectionScope, createFocusGroupCollectionScope]\n);\n\ntype ContentData = {\n  ref?: React.Ref<ViewportContentMounterElement>;\n} & ViewportContentMounterProps;\n\ntype NavigationMenuContextValue = {\n  isRootMenu: boolean;\n  value: string;\n  previousValue: string;\n  baseId: string;\n  dir: Direction;\n  orientation: Orientation;\n  rootNavigationMenu: NavigationMenuElement | null;\n  indicatorTrack: HTMLDivElement | null;\n  onIndicatorTrackChange(indicatorTrack: HTMLDivElement | null): void;\n  viewport: NavigationMenuViewportElement | null;\n  onViewportChange(viewport: NavigationMenuViewportElement | null): void;\n  onViewportContentChange(contentValue: string, contentData: ContentData): void;\n  onViewportContentRemove(contentValue: string): void;\n  onTriggerEnter(itemValue: string): void;\n  onTriggerLeave(): void;\n  onContentEnter(): void;\n  onContentLeave(): void;\n  onItemSelect(itemValue: string): void;\n  onItemDismiss(): void;\n};\n\nconst [NavigationMenuProviderImpl, useNavigationMenuContext] =\n  createNavigationMenuContext<NavigationMenuContextValue>(NAVIGATION_MENU_NAME);\n\nconst [ViewportContentProvider, useViewportContentContext] = createNavigationMenuContext<{\n  items: Map<string, ContentData>;\n}>(NAVIGATION_MENU_NAME);\n\ntype NavigationMenuElement = React.ComponentRef<typeof Primitive.nav>;\ntype PrimitiveNavProps = React.ComponentPropsWithoutRef<typeof Primitive.nav>;\ninterface NavigationMenuProps\n  extends Omit<NavigationMenuProviderProps, keyof NavigationMenuProviderPrivateProps>,\n    PrimitiveNavProps {\n  value?: string;\n  defaultValue?: string;\n  onValueChange?: (value: string) => void;\n  dir?: Direction;\n  orientation?: Orientation;\n  /**\n   * The duration from when the pointer enters the trigger until the tooltip gets opened.\n   * @defaultValue 200\n   */\n  delayDuration?: number;\n  /**\n   * How much time a user has to enter another trigger without incurring a delay again.\n   * @defaultValue 300\n   */\n  skipDelayDuration?: number;\n}\n\nconst NavigationMenu = React.forwardRef<NavigationMenuElement, NavigationMenuProps>(\n  (props: ScopedProps<NavigationMenuProps>, forwardedRef) => {\n    const {\n      __scopeNavigationMenu,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      delayDuration = 200,\n      skipDelayDuration = 300,\n      orientation = 'horizontal',\n      dir,\n      ...NavigationMenuProps\n    } = props;\n    const [navigationMenu, setNavigationMenu] = React.useState<NavigationMenuElement | null>(null);\n    const composedRef = useComposedRefs(forwardedRef, (node) => setNavigationMenu(node));\n    const direction = useDirection(dir);\n    const openTimerRef = React.useRef(0);\n    const closeTimerRef = React.useRef(0);\n    const skipDelayTimerRef = React.useRef(0);\n    const [isOpenDelayed, setIsOpenDelayed] = React.useState(true);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: (value) => {\n        const isOpen = value !== '';\n        const hasSkipDelayDuration = skipDelayDuration > 0;\n\n        if (isOpen) {\n          window.clearTimeout(skipDelayTimerRef.current);\n          if (hasSkipDelayDuration) setIsOpenDelayed(false);\n        } else {\n          window.clearTimeout(skipDelayTimerRef.current);\n          skipDelayTimerRef.current = window.setTimeout(\n            () => setIsOpenDelayed(true),\n            skipDelayDuration\n          );\n        }\n\n        onValueChange?.(value);\n      },\n      defaultProp: defaultValue ?? '',\n      caller: NAVIGATION_MENU_NAME,\n    });\n\n    const startCloseTimer = React.useCallback(() => {\n      window.clearTimeout(closeTimerRef.current);\n      closeTimerRef.current = window.setTimeout(() => setValue(''), 150);\n    }, [setValue]);\n\n    const handleOpen = React.useCallback(\n      (itemValue: string) => {\n        window.clearTimeout(closeTimerRef.current);\n        setValue(itemValue);\n      },\n      [setValue]\n    );\n\n    const handleDelayedOpen = React.useCallback(\n      (itemValue: string) => {\n        const isOpenItem = value === itemValue;\n        if (isOpenItem) {\n          // If the item is already open (e.g. we're transitioning from the content to the trigger)\n          // then we want to clear the close timer immediately.\n          window.clearTimeout(closeTimerRef.current);\n        } else {\n          openTimerRef.current = window.setTimeout(() => {\n            window.clearTimeout(closeTimerRef.current);\n            setValue(itemValue);\n          }, delayDuration);\n        }\n      },\n      [value, setValue, delayDuration]\n    );\n\n    React.useEffect(() => {\n      return () => {\n        window.clearTimeout(openTimerRef.current);\n        window.clearTimeout(closeTimerRef.current);\n        window.clearTimeout(skipDelayTimerRef.current);\n      };\n    }, []);\n\n    return (\n      <NavigationMenuProvider\n        scope={__scopeNavigationMenu}\n        isRootMenu={true}\n        value={value}\n        dir={direction}\n        orientation={orientation}\n        rootNavigationMenu={navigationMenu}\n        onTriggerEnter={(itemValue) => {\n          window.clearTimeout(openTimerRef.current);\n          if (isOpenDelayed) handleDelayedOpen(itemValue);\n          else handleOpen(itemValue);\n        }}\n        onTriggerLeave={() => {\n          window.clearTimeout(openTimerRef.current);\n          startCloseTimer();\n        }}\n        onContentEnter={() => window.clearTimeout(closeTimerRef.current)}\n        onContentLeave={startCloseTimer}\n        onItemSelect={(itemValue) => {\n          setValue((prevValue) => (prevValue === itemValue ? '' : itemValue));\n        }}\n        onItemDismiss={() => setValue('')}\n      >\n        <Primitive.nav\n          aria-label=\"Main\"\n          data-orientation={orientation}\n          dir={direction}\n          {...NavigationMenuProps}\n          ref={composedRef}\n        />\n      </NavigationMenuProvider>\n    );\n  }\n);\n\nNavigationMenu.displayName = NAVIGATION_MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuSub\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_NAME = 'NavigationMenuSub';\n\ntype NavigationMenuSubElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface NavigationMenuSubProps\n  extends Omit<NavigationMenuProviderProps, keyof NavigationMenuProviderPrivateProps>,\n    PrimitiveDivProps {\n  value?: string;\n  defaultValue?: string;\n  onValueChange?: (value: string) => void;\n  orientation?: Orientation;\n}\n\nconst NavigationMenuSub = React.forwardRef<NavigationMenuSubElement, NavigationMenuSubProps>(\n  (props: ScopedProps<NavigationMenuSubProps>, forwardedRef) => {\n    const {\n      __scopeNavigationMenu,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = 'horizontal',\n      ...subProps\n    } = props;\n    const context = useNavigationMenuContext(SUB_NAME, __scopeNavigationMenu);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue ?? '',\n      caller: SUB_NAME,\n    });\n\n    return (\n      <NavigationMenuProvider\n        scope={__scopeNavigationMenu}\n        isRootMenu={false}\n        value={value}\n        dir={context.dir}\n        orientation={orientation}\n        rootNavigationMenu={context.rootNavigationMenu}\n        onTriggerEnter={(itemValue) => setValue(itemValue)}\n        onItemSelect={(itemValue) => setValue(itemValue)}\n        onItemDismiss={() => setValue('')}\n      >\n        <Primitive.div data-orientation={orientation} {...subProps} ref={forwardedRef} />\n      </NavigationMenuProvider>\n    );\n  }\n);\n\nNavigationMenuSub.displayName = SUB_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ninterface NavigationMenuProviderPrivateProps {\n  isRootMenu: boolean;\n  scope: Scope;\n  children: React.ReactNode;\n  orientation: Orientation;\n  dir: Direction;\n  rootNavigationMenu: NavigationMenuElement | null;\n  value: string;\n  onTriggerEnter(itemValue: string): void;\n  onTriggerLeave?(): void;\n  onContentEnter?(): void;\n  onContentLeave?(): void;\n  onItemSelect(itemValue: string): void;\n  onItemDismiss(): void;\n}\n\ninterface NavigationMenuProviderProps extends NavigationMenuProviderPrivateProps {}\n\nconst NavigationMenuProvider: React.FC<NavigationMenuProviderProps> = (\n  props: ScopedProps<NavigationMenuProviderProps>\n) => {\n  const {\n    scope,\n    isRootMenu,\n    rootNavigationMenu,\n    dir,\n    orientation,\n    children,\n    value,\n    onItemSelect,\n    onItemDismiss,\n    onTriggerEnter,\n    onTriggerLeave,\n    onContentEnter,\n    onContentLeave,\n  } = props;\n  const [viewport, setViewport] = React.useState<NavigationMenuViewportElement | null>(null);\n  const [viewportContent, setViewportContent] = React.useState<Map<string, ContentData>>(new Map());\n  const [indicatorTrack, setIndicatorTrack] = React.useState<HTMLDivElement | null>(null);\n\n  return (\n    <NavigationMenuProviderImpl\n      scope={scope}\n      isRootMenu={isRootMenu}\n      rootNavigationMenu={rootNavigationMenu}\n      value={value}\n      previousValue={usePrevious(value)}\n      baseId={useId()}\n      dir={dir}\n      orientation={orientation}\n      viewport={viewport}\n      onViewportChange={setViewport}\n      indicatorTrack={indicatorTrack}\n      onIndicatorTrackChange={setIndicatorTrack}\n      onTriggerEnter={useCallbackRef(onTriggerEnter)}\n      onTriggerLeave={useCallbackRef(onTriggerLeave)}\n      onContentEnter={useCallbackRef(onContentEnter)}\n      onContentLeave={useCallbackRef(onContentLeave)}\n      onItemSelect={useCallbackRef(onItemSelect)}\n      onItemDismiss={useCallbackRef(onItemDismiss)}\n      onViewportContentChange={React.useCallback((contentValue, contentData) => {\n        setViewportContent((prevContent) => {\n          prevContent.set(contentValue, contentData);\n          return new Map(prevContent);\n        });\n      }, [])}\n      onViewportContentRemove={React.useCallback((contentValue) => {\n        setViewportContent((prevContent) => {\n          if (!prevContent.has(contentValue)) return prevContent;\n          prevContent.delete(contentValue);\n          return new Map(prevContent);\n        });\n      }, [])}\n    >\n      <Collection.Provider scope={scope}>\n        <ViewportContentProvider scope={scope} items={viewportContent}>\n          {children}\n        </ViewportContentProvider>\n      </Collection.Provider>\n    </NavigationMenuProviderImpl>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuList\n * -----------------------------------------------------------------------------------------------*/\n\nconst LIST_NAME = 'NavigationMenuList';\n\ntype NavigationMenuListElement = React.ComponentRef<typeof Primitive.ul>;\ntype PrimitiveUnorderedListProps = React.ComponentPropsWithoutRef<typeof Primitive.ul>;\ninterface NavigationMenuListProps extends PrimitiveUnorderedListProps {}\n\nconst NavigationMenuList = React.forwardRef<NavigationMenuListElement, NavigationMenuListProps>(\n  (props: ScopedProps<NavigationMenuListProps>, forwardedRef) => {\n    const { __scopeNavigationMenu, ...listProps } = props;\n    const context = useNavigationMenuContext(LIST_NAME, __scopeNavigationMenu);\n\n    const list = (\n      <Primitive.ul data-orientation={context.orientation} {...listProps} ref={forwardedRef} />\n    );\n\n    return (\n      <Primitive.div style={{ position: 'relative' }} ref={context.onIndicatorTrackChange}>\n        <Collection.Slot scope={__scopeNavigationMenu}>\n          {context.isRootMenu ? <FocusGroup asChild>{list}</FocusGroup> : list}\n        </Collection.Slot>\n      </Primitive.div>\n    );\n  }\n);\n\nNavigationMenuList.displayName = LIST_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'NavigationMenuItem';\n\ntype FocusProxyElement = React.ComponentRef<typeof VisuallyHiddenPrimitive.Root>;\n\ntype NavigationMenuItemContextValue = {\n  value: string;\n  triggerRef: React.RefObject<NavigationMenuTriggerElement | null>;\n  contentRef: React.RefObject<NavigationMenuContentElement | null>;\n  focusProxyRef: React.RefObject<FocusProxyElement | null>;\n  wasEscapeCloseRef: React.MutableRefObject<boolean>;\n  onEntryKeyDown(): void;\n  onFocusProxyEnter(side: 'start' | 'end'): void;\n  onRootContentClose(): void;\n  onContentFocusOutside(): void;\n};\n\nconst [NavigationMenuItemContextProvider, useNavigationMenuItemContext] =\n  createNavigationMenuContext<NavigationMenuItemContextValue>(ITEM_NAME);\n\ntype NavigationMenuItemElement = React.ComponentRef<typeof Primitive.li>;\ntype PrimitiveListItemProps = React.ComponentPropsWithoutRef<typeof Primitive.li>;\ninterface NavigationMenuItemProps extends PrimitiveListItemProps {\n  value?: string;\n}\n\nconst NavigationMenuItem = React.forwardRef<NavigationMenuItemElement, NavigationMenuItemProps>(\n  (props: ScopedProps<NavigationMenuItemProps>, forwardedRef) => {\n    const { __scopeNavigationMenu, value: valueProp, ...itemProps } = props;\n    const autoValue = useId();\n    // We need to provide an initial deterministic value as `useId` will return\n    // empty string on the first render and we don't want to match our internal \"closed\" value.\n    const value = valueProp || autoValue || 'LEGACY_REACT_AUTO_VALUE';\n    const contentRef = React.useRef<NavigationMenuContentElement>(null);\n    const triggerRef = React.useRef<NavigationMenuTriggerElement>(null);\n    const focusProxyRef = React.useRef<FocusProxyElement>(null);\n    const restoreContentTabOrderRef = React.useRef(() => {});\n    const wasEscapeCloseRef = React.useRef(false);\n\n    const handleContentEntry = React.useCallback((side = 'start') => {\n      if (contentRef.current) {\n        restoreContentTabOrderRef.current();\n        const candidates = getTabbableCandidates(contentRef.current);\n        if (candidates.length) focusFirst(side === 'start' ? candidates : candidates.reverse());\n      }\n    }, []);\n\n    const handleContentExit = React.useCallback(() => {\n      if (contentRef.current) {\n        const candidates = getTabbableCandidates(contentRef.current);\n        if (candidates.length) restoreContentTabOrderRef.current = removeFromTabOrder(candidates);\n      }\n    }, []);\n\n    return (\n      <NavigationMenuItemContextProvider\n        scope={__scopeNavigationMenu}\n        value={value}\n        triggerRef={triggerRef}\n        contentRef={contentRef}\n        focusProxyRef={focusProxyRef}\n        wasEscapeCloseRef={wasEscapeCloseRef}\n        onEntryKeyDown={handleContentEntry}\n        onFocusProxyEnter={handleContentEntry}\n        onRootContentClose={handleContentExit}\n        onContentFocusOutside={handleContentExit}\n      >\n        <Primitive.li {...itemProps} ref={forwardedRef} />\n      </NavigationMenuItemContextProvider>\n    );\n  }\n);\n\nNavigationMenuItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'NavigationMenuTrigger';\n\ntype NavigationMenuTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface NavigationMenuTriggerProps extends PrimitiveButtonProps {}\n\nconst NavigationMenuTrigger = React.forwardRef<\n  NavigationMenuTriggerElement,\n  NavigationMenuTriggerProps\n>((props: ScopedProps<NavigationMenuTriggerProps>, forwardedRef) => {\n  const { __scopeNavigationMenu, disabled, ...triggerProps } = props;\n  const context = useNavigationMenuContext(TRIGGER_NAME, props.__scopeNavigationMenu);\n  const itemContext = useNavigationMenuItemContext(TRIGGER_NAME, props.__scopeNavigationMenu);\n  const ref = React.useRef<NavigationMenuTriggerElement>(null);\n  const composedRefs = useComposedRefs(ref, itemContext.triggerRef, forwardedRef);\n  const triggerId = makeTriggerId(context.baseId, itemContext.value);\n  const contentId = makeContentId(context.baseId, itemContext.value);\n  const hasPointerMoveOpenedRef = React.useRef(false);\n  const wasClickCloseRef = React.useRef(false);\n  const open = itemContext.value === context.value;\n\n  return (\n    <>\n      <Collection.ItemSlot scope={__scopeNavigationMenu} value={itemContext.value}>\n        <FocusGroupItem asChild>\n          <Primitive.button\n            id={triggerId}\n            disabled={disabled}\n            data-disabled={disabled ? '' : undefined}\n            data-state={getOpenState(open)}\n            aria-expanded={open}\n            aria-controls={contentId}\n            {...triggerProps}\n            ref={composedRefs}\n            onPointerEnter={composeEventHandlers(props.onPointerEnter, () => {\n              wasClickCloseRef.current = false;\n              itemContext.wasEscapeCloseRef.current = false;\n            })}\n            onPointerMove={composeEventHandlers(\n              props.onPointerMove,\n              whenMouse(() => {\n                if (\n                  disabled ||\n                  wasClickCloseRef.current ||\n                  itemContext.wasEscapeCloseRef.current ||\n                  hasPointerMoveOpenedRef.current\n                )\n                  return;\n                context.onTriggerEnter(itemContext.value);\n                hasPointerMoveOpenedRef.current = true;\n              })\n            )}\n            onPointerLeave={composeEventHandlers(\n              props.onPointerLeave,\n              whenMouse(() => {\n                if (disabled) return;\n                context.onTriggerLeave();\n                hasPointerMoveOpenedRef.current = false;\n              })\n            )}\n            onClick={composeEventHandlers(props.onClick, () => {\n              context.onItemSelect(itemContext.value);\n              wasClickCloseRef.current = open;\n            })}\n            onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n              const verticalEntryKey = context.dir === 'rtl' ? 'ArrowLeft' : 'ArrowRight';\n              const entryKey = { horizontal: 'ArrowDown', vertical: verticalEntryKey }[\n                context.orientation\n              ];\n              if (open && event.key === entryKey) {\n                itemContext.onEntryKeyDown();\n                // Prevent FocusGroupItem from handling the event\n                event.preventDefault();\n              }\n            })}\n          />\n        </FocusGroupItem>\n      </Collection.ItemSlot>\n\n      {/* Proxy tab order between trigger and content */}\n      {open && (\n        <>\n          <VisuallyHiddenPrimitive.Root\n            aria-hidden\n            tabIndex={0}\n            ref={itemContext.focusProxyRef}\n            onFocus={(event) => {\n              const content = itemContext.contentRef.current;\n              const prevFocusedElement = event.relatedTarget as HTMLElement | null;\n              const wasTriggerFocused = prevFocusedElement === ref.current;\n              const wasFocusFromContent = content?.contains(prevFocusedElement);\n\n              if (wasTriggerFocused || !wasFocusFromContent) {\n                itemContext.onFocusProxyEnter(wasTriggerFocused ? 'start' : 'end');\n              }\n            }}\n          />\n\n          {/* Restructure a11y tree to make content accessible to screen reader when using the viewport */}\n          {context.viewport && <span aria-owns={contentId} />}\n        </>\n      )}\n    </>\n  );\n});\n\nNavigationMenuTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuLink\n * -----------------------------------------------------------------------------------------------*/\n\nconst LINK_NAME = 'NavigationMenuLink';\nconst LINK_SELECT = 'navigationMenu.linkSelect';\n\ntype NavigationMenuLinkElement = React.ComponentRef<typeof Primitive.a>;\ntype PrimitiveLinkProps = React.ComponentPropsWithoutRef<typeof Primitive.a>;\ninterface NavigationMenuLinkProps extends Omit<PrimitiveLinkProps, 'onSelect'> {\n  active?: boolean;\n  onSelect?: (event: Event) => void;\n}\n\nconst NavigationMenuLink = React.forwardRef<NavigationMenuLinkElement, NavigationMenuLinkProps>(\n  (props: ScopedProps<NavigationMenuLinkProps>, forwardedRef) => {\n    const { __scopeNavigationMenu, active, onSelect, ...linkProps } = props;\n\n    return (\n      <FocusGroupItem asChild>\n        <Primitive.a\n          data-active={active ? '' : undefined}\n          aria-current={active ? 'page' : undefined}\n          {...linkProps}\n          ref={forwardedRef}\n          onClick={composeEventHandlers(\n            props.onClick,\n            (event) => {\n              const target = event.target as HTMLElement;\n              const linkSelectEvent = new CustomEvent(LINK_SELECT, {\n                bubbles: true,\n                cancelable: true,\n              });\n              target.addEventListener(LINK_SELECT, (event) => onSelect?.(event), { once: true });\n              dispatchDiscreteCustomEvent(target, linkSelectEvent);\n\n              if (!linkSelectEvent.defaultPrevented && !event.metaKey) {\n                const rootContentDismissEvent = new CustomEvent(ROOT_CONTENT_DISMISS, {\n                  bubbles: true,\n                  cancelable: true,\n                });\n                dispatchDiscreteCustomEvent(target, rootContentDismissEvent);\n              }\n            },\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </FocusGroupItem>\n    );\n  }\n);\n\nNavigationMenuLink.displayName = LINK_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'NavigationMenuIndicator';\n\ntype NavigationMenuIndicatorElement = NavigationMenuIndicatorImplElement;\ninterface NavigationMenuIndicatorProps extends NavigationMenuIndicatorImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst NavigationMenuIndicator = React.forwardRef<\n  NavigationMenuIndicatorElement,\n  NavigationMenuIndicatorProps\n>((props: ScopedProps<NavigationMenuIndicatorProps>, forwardedRef) => {\n  const { forceMount, ...indicatorProps } = props;\n  const context = useNavigationMenuContext(INDICATOR_NAME, props.__scopeNavigationMenu);\n  const isVisible = Boolean(context.value);\n\n  return context.indicatorTrack\n    ? ReactDOM.createPortal(\n        <Presence present={forceMount || isVisible}>\n          <NavigationMenuIndicatorImpl {...indicatorProps} ref={forwardedRef} />\n        </Presence>,\n        context.indicatorTrack\n      )\n    : null;\n});\n\nNavigationMenuIndicator.displayName = INDICATOR_NAME;\n\ntype NavigationMenuIndicatorImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface NavigationMenuIndicatorImplProps extends PrimitiveDivProps {}\n\nconst NavigationMenuIndicatorImpl = React.forwardRef<\n  NavigationMenuIndicatorImplElement,\n  NavigationMenuIndicatorImplProps\n>((props: ScopedProps<NavigationMenuIndicatorImplProps>, forwardedRef) => {\n  const { __scopeNavigationMenu, ...indicatorProps } = props;\n  const context = useNavigationMenuContext(INDICATOR_NAME, __scopeNavigationMenu);\n  const getItems = useCollection(__scopeNavigationMenu);\n  const [activeTrigger, setActiveTrigger] = React.useState<NavigationMenuTriggerElement | null>(\n    null\n  );\n  const [position, setPosition] = React.useState<{ size: number; offset: number } | null>(null);\n  const isHorizontal = context.orientation === 'horizontal';\n  const isVisible = Boolean(context.value);\n\n  React.useEffect(() => {\n    const items = getItems();\n    const triggerNode = items.find((item) => item.value === context.value)?.ref.current;\n    if (triggerNode) setActiveTrigger(triggerNode);\n  }, [getItems, context.value]);\n\n  /**\n   * Update position when the indicator or parent track size changes\n   */\n  const handlePositionChange = () => {\n    if (activeTrigger) {\n      setPosition({\n        size: isHorizontal ? activeTrigger.offsetWidth : activeTrigger.offsetHeight,\n        offset: isHorizontal ? activeTrigger.offsetLeft : activeTrigger.offsetTop,\n      });\n    }\n  };\n  useResizeObserver(activeTrigger, handlePositionChange);\n  useResizeObserver(context.indicatorTrack, handlePositionChange);\n\n  // We need to wait for the indicator position to be available before rendering to\n  // snap immediately into position rather than transitioning from initial\n  return position ? (\n    <Primitive.div\n      aria-hidden\n      data-state={isVisible ? 'visible' : 'hidden'}\n      data-orientation={context.orientation}\n      {...indicatorProps}\n      ref={forwardedRef}\n      style={{\n        position: 'absolute',\n        ...(isHorizontal\n          ? {\n              left: 0,\n              width: position.size + 'px',\n              transform: `translateX(${position.offset}px)`,\n            }\n          : {\n              top: 0,\n              height: position.size + 'px',\n              transform: `translateY(${position.offset}px)`,\n            }),\n        ...indicatorProps.style,\n      }}\n    />\n  ) : null;\n});\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'NavigationMenuContent';\n\ntype NavigationMenuContentElement = NavigationMenuContentImplElement;\ninterface NavigationMenuContentProps\n  extends Omit<NavigationMenuContentImplProps, keyof NavigationMenuContentImplPrivateProps> {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst NavigationMenuContent = React.forwardRef<\n  NavigationMenuContentElement,\n  NavigationMenuContentProps\n>((props: ScopedProps<NavigationMenuContentProps>, forwardedRef) => {\n  const { forceMount, ...contentProps } = props;\n  const context = useNavigationMenuContext(CONTENT_NAME, props.__scopeNavigationMenu);\n  const itemContext = useNavigationMenuItemContext(CONTENT_NAME, props.__scopeNavigationMenu);\n  const composedRefs = useComposedRefs(itemContext.contentRef, forwardedRef);\n  const open = itemContext.value === context.value;\n\n  const commonProps = {\n    value: itemContext.value,\n    triggerRef: itemContext.triggerRef,\n    focusProxyRef: itemContext.focusProxyRef,\n    wasEscapeCloseRef: itemContext.wasEscapeCloseRef,\n    onContentFocusOutside: itemContext.onContentFocusOutside,\n    onRootContentClose: itemContext.onRootContentClose,\n    ...contentProps,\n  };\n\n  return !context.viewport ? (\n    <Presence present={forceMount || open}>\n      <NavigationMenuContentImpl\n        data-state={getOpenState(open)}\n        {...commonProps}\n        ref={composedRefs}\n        onPointerEnter={composeEventHandlers(props.onPointerEnter, context.onContentEnter)}\n        onPointerLeave={composeEventHandlers(\n          props.onPointerLeave,\n          whenMouse(context.onContentLeave)\n        )}\n        style={{\n          // Prevent interaction when animating out\n          pointerEvents: !open && context.isRootMenu ? 'none' : undefined,\n          ...commonProps.style,\n        }}\n      />\n    </Presence>\n  ) : (\n    <ViewportContentMounter forceMount={forceMount} {...commonProps} ref={composedRefs} />\n  );\n});\n\nNavigationMenuContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ViewportContentMounterElement = NavigationMenuContentImplElement;\ninterface ViewportContentMounterProps extends NavigationMenuContentImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst ViewportContentMounter = React.forwardRef<\n  ViewportContentMounterElement,\n  ViewportContentMounterProps\n>((props: ScopedProps<ViewportContentMounterProps>, forwardedRef) => {\n  const context = useNavigationMenuContext(CONTENT_NAME, props.__scopeNavigationMenu);\n  const { onViewportContentChange, onViewportContentRemove } = context;\n\n  useLayoutEffect(() => {\n    onViewportContentChange(props.value, {\n      ref: forwardedRef,\n      ...props,\n    });\n  }, [props, forwardedRef, onViewportContentChange]);\n\n  useLayoutEffect(() => {\n    return () => onViewportContentRemove(props.value);\n  }, [props.value, onViewportContentRemove]);\n\n  // Content is proxied into the viewport\n  return null;\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst ROOT_CONTENT_DISMISS = 'navigationMenu.rootContentDismiss';\n\ntype MotionAttribute = 'to-start' | 'to-end' | 'from-start' | 'from-end';\ntype NavigationMenuContentImplElement = React.ComponentRef<typeof DismissableLayer>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\n\ninterface NavigationMenuContentImplPrivateProps {\n  value: string;\n  triggerRef: React.RefObject<NavigationMenuTriggerElement | null>;\n  focusProxyRef: React.RefObject<FocusProxyElement | null>;\n  wasEscapeCloseRef: React.MutableRefObject<boolean>;\n  onContentFocusOutside(): void;\n  onRootContentClose(): void;\n}\ninterface NavigationMenuContentImplProps\n  extends Omit<DismissableLayerProps, 'onDismiss' | 'disableOutsidePointerEvents'>,\n    NavigationMenuContentImplPrivateProps {}\n\nconst NavigationMenuContentImpl = React.forwardRef<\n  NavigationMenuContentImplElement,\n  NavigationMenuContentImplProps\n>((props: ScopedProps<NavigationMenuContentImplProps>, forwardedRef) => {\n  const {\n    __scopeNavigationMenu,\n    value,\n    triggerRef,\n    focusProxyRef,\n    wasEscapeCloseRef,\n    onRootContentClose,\n    onContentFocusOutside,\n    ...contentProps\n  } = props;\n  const context = useNavigationMenuContext(CONTENT_NAME, __scopeNavigationMenu);\n  const ref = React.useRef<NavigationMenuContentImplElement>(null);\n  const composedRefs = useComposedRefs(ref, forwardedRef);\n  const triggerId = makeTriggerId(context.baseId, value);\n  const contentId = makeContentId(context.baseId, value);\n  const getItems = useCollection(__scopeNavigationMenu);\n  const prevMotionAttributeRef = React.useRef<MotionAttribute | null>(null);\n\n  const { onItemDismiss } = context;\n\n  React.useEffect(() => {\n    const content = ref.current;\n\n    // Bubble dismiss to the root content node and focus its trigger\n    if (context.isRootMenu && content) {\n      const handleClose = () => {\n        onItemDismiss();\n        onRootContentClose();\n        if (content.contains(document.activeElement)) triggerRef.current?.focus();\n      };\n      content.addEventListener(ROOT_CONTENT_DISMISS, handleClose);\n      return () => content.removeEventListener(ROOT_CONTENT_DISMISS, handleClose);\n    }\n  }, [context.isRootMenu, props.value, triggerRef, onItemDismiss, onRootContentClose]);\n\n  const motionAttribute = React.useMemo(() => {\n    const items = getItems();\n    const values = items.map((item) => item.value);\n    if (context.dir === 'rtl') values.reverse();\n    const index = values.indexOf(context.value);\n    const prevIndex = values.indexOf(context.previousValue);\n    const isSelected = value === context.value;\n    const wasSelected = prevIndex === values.indexOf(value);\n\n    // We only want to update selected and the last selected content\n    // this avoids animations being interrupted outside of that range\n    if (!isSelected && !wasSelected) return prevMotionAttributeRef.current;\n\n    const attribute = (() => {\n      // Don't provide a direction on the initial open\n      if (index !== prevIndex) {\n        // If we're moving to this item from another\n        if (isSelected && prevIndex !== -1) return index > prevIndex ? 'from-end' : 'from-start';\n        // If we're leaving this item for another\n        if (wasSelected && index !== -1) return index > prevIndex ? 'to-start' : 'to-end';\n      }\n      // Otherwise we're entering from closed or leaving the list\n      // entirely and should not animate in any direction\n      return null;\n    })();\n\n    prevMotionAttributeRef.current = attribute;\n    return attribute;\n  }, [context.previousValue, context.value, context.dir, getItems, value]);\n\n  return (\n    <FocusGroup asChild>\n      <DismissableLayer\n        id={contentId}\n        aria-labelledby={triggerId}\n        data-motion={motionAttribute}\n        data-orientation={context.orientation}\n        {...contentProps}\n        ref={composedRefs}\n        disableOutsidePointerEvents={false}\n        onDismiss={() => {\n          const rootContentDismissEvent = new Event(ROOT_CONTENT_DISMISS, {\n            bubbles: true,\n            cancelable: true,\n          });\n          ref.current?.dispatchEvent(rootContentDismissEvent);\n        }}\n        onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) => {\n          onContentFocusOutside();\n          const target = event.target as HTMLElement;\n          // Only dismiss content when focus moves outside of the menu\n          if (context.rootNavigationMenu?.contains(target)) event.preventDefault();\n        })}\n        onPointerDownOutside={composeEventHandlers(props.onPointerDownOutside, (event) => {\n          const target = event.target as HTMLElement;\n          const isTrigger = getItems().some((item) => item.ref.current?.contains(target));\n          const isRootViewport = context.isRootMenu && context.viewport?.contains(target);\n          if (isTrigger || isRootViewport || !context.isRootMenu) event.preventDefault();\n        })}\n        onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n          const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n          const isTabKey = event.key === 'Tab' && !isMetaKey;\n          if (isTabKey) {\n            const candidates = getTabbableCandidates(event.currentTarget);\n            const focusedElement = document.activeElement;\n            const index = candidates.findIndex((candidate) => candidate === focusedElement);\n            const isMovingBackwards = event.shiftKey;\n            const nextCandidates = isMovingBackwards\n              ? candidates.slice(0, index).reverse()\n              : candidates.slice(index + 1, candidates.length);\n\n            if (focusFirst(nextCandidates)) {\n              // prevent browser tab keydown because we've handled focus\n              event.preventDefault();\n            } else {\n              // If we can't focus that means we're at the edges\n              // so focus the proxy and let browser handle\n              // tab/shift+tab keypress on the proxy instead\n              focusProxyRef.current?.focus();\n            }\n          }\n        })}\n        onEscapeKeyDown={composeEventHandlers(props.onEscapeKeyDown, (_event) => {\n          // prevent the dropdown from reopening\n          // after the escape key has been pressed\n          wasEscapeCloseRef.current = true;\n        })}\n      />\n    </FocusGroup>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuViewport\n * -----------------------------------------------------------------------------------------------*/\n\nconst VIEWPORT_NAME = 'NavigationMenuViewport';\n\ntype NavigationMenuViewportElement = NavigationMenuViewportImplElement;\ninterface NavigationMenuViewportProps\n  extends Omit<NavigationMenuViewportImplProps, 'activeContentValue'> {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst NavigationMenuViewport = React.forwardRef<\n  NavigationMenuViewportElement,\n  NavigationMenuViewportProps\n>((props: ScopedProps<NavigationMenuViewportProps>, forwardedRef) => {\n  const { forceMount, ...viewportProps } = props;\n  const context = useNavigationMenuContext(VIEWPORT_NAME, props.__scopeNavigationMenu);\n  const open = Boolean(context.value);\n\n  return (\n    <Presence present={forceMount || open}>\n      <NavigationMenuViewportImpl {...viewportProps} ref={forwardedRef} />\n    </Presence>\n  );\n});\n\nNavigationMenuViewport.displayName = VIEWPORT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype NavigationMenuViewportImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface NavigationMenuViewportImplProps extends PrimitiveDivProps {}\n\nconst NavigationMenuViewportImpl = React.forwardRef<\n  NavigationMenuViewportImplElement,\n  NavigationMenuViewportImplProps\n>((props: ScopedProps<NavigationMenuViewportImplProps>, forwardedRef) => {\n  const { __scopeNavigationMenu, children, ...viewportImplProps } = props;\n  const context = useNavigationMenuContext(VIEWPORT_NAME, __scopeNavigationMenu);\n  const composedRefs = useComposedRefs(forwardedRef, context.onViewportChange);\n  const viewportContentContext = useViewportContentContext(\n    CONTENT_NAME,\n    props.__scopeNavigationMenu\n  );\n  const [size, setSize] = React.useState<{ width: number; height: number } | null>(null);\n  const [content, setContent] = React.useState<NavigationMenuContentElement | null>(null);\n  const viewportWidth = size ? size?.width + 'px' : undefined;\n  const viewportHeight = size ? size?.height + 'px' : undefined;\n  const open = Boolean(context.value);\n  // We persist the last active content value as the viewport may be animating out\n  // and we want the content to remain mounted for the lifecycle of the viewport.\n  const activeContentValue = open ? context.value : context.previousValue;\n\n  /**\n   * Update viewport size to match the active content node.\n   * We prefer offset dimensions over `getBoundingClientRect` as the latter respects CSS transform.\n   * For example, if content animates in from `scale(0.5)` the dimensions would be anything\n   * from `0.5` to `1` of the intended size.\n   */\n  const handleSizeChange = () => {\n    if (content) setSize({ width: content.offsetWidth, height: content.offsetHeight });\n  };\n  useResizeObserver(content, handleSizeChange);\n\n  return (\n    <Primitive.div\n      data-state={getOpenState(open)}\n      data-orientation={context.orientation}\n      {...viewportImplProps}\n      ref={composedRefs}\n      style={{\n        // Prevent interaction when animating out\n        pointerEvents: !open && context.isRootMenu ? 'none' : undefined,\n        ['--radix-navigation-menu-viewport-width' as any]: viewportWidth,\n        ['--radix-navigation-menu-viewport-height' as any]: viewportHeight,\n        ...viewportImplProps.style,\n      }}\n      onPointerEnter={composeEventHandlers(props.onPointerEnter, context.onContentEnter)}\n      onPointerLeave={composeEventHandlers(props.onPointerLeave, whenMouse(context.onContentLeave))}\n    >\n      {Array.from(viewportContentContext.items).map(([value, { ref, forceMount, ...props }]) => {\n        const isActive = activeContentValue === value;\n        return (\n          <Presence key={value} present={forceMount || isActive}>\n            <NavigationMenuContentImpl\n              {...props}\n              ref={composeRefs(ref, (node) => {\n                // We only want to update the stored node when another is available\n                // as we need to smoothly transition between them.\n                if (isActive && node) setContent(node);\n              })}\n            />\n          </Presence>\n        );\n      })}\n    </Primitive.div>\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_GROUP_NAME = 'FocusGroup';\n\ntype FocusGroupElement = React.ComponentRef<typeof Primitive.div>;\ninterface FocusGroupProps extends PrimitiveDivProps {}\n\nconst FocusGroup = React.forwardRef<FocusGroupElement, FocusGroupProps>(\n  (props: ScopedProps<FocusGroupProps>, forwardedRef) => {\n    const { __scopeNavigationMenu, ...groupProps } = props;\n    const context = useNavigationMenuContext(FOCUS_GROUP_NAME, __scopeNavigationMenu);\n\n    return (\n      <FocusGroupCollection.Provider scope={__scopeNavigationMenu}>\n        <FocusGroupCollection.Slot scope={__scopeNavigationMenu}>\n          <Primitive.div dir={context.dir} {...groupProps} ref={forwardedRef} />\n        </FocusGroupCollection.Slot>\n      </FocusGroupCollection.Provider>\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_KEYS = ['ArrowRight', 'ArrowLeft', 'ArrowUp', 'ArrowDown'];\nconst FOCUS_GROUP_ITEM_NAME = 'FocusGroupItem';\n\ntype FocusGroupItemElement = React.ComponentRef<typeof Primitive.button>;\ninterface FocusGroupItemProps extends PrimitiveButtonProps {}\n\nconst FocusGroupItem = React.forwardRef<FocusGroupItemElement, FocusGroupItemProps>(\n  (props: ScopedProps<FocusGroupItemProps>, forwardedRef) => {\n    const { __scopeNavigationMenu, ...groupProps } = props;\n    const getItems = useFocusGroupCollection(__scopeNavigationMenu);\n    const context = useNavigationMenuContext(FOCUS_GROUP_ITEM_NAME, __scopeNavigationMenu);\n\n    return (\n      <FocusGroupCollection.ItemSlot scope={__scopeNavigationMenu}>\n        <Primitive.button\n          {...groupProps}\n          ref={forwardedRef}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            const isFocusNavigationKey = ['Home', 'End', ...ARROW_KEYS].includes(event.key);\n            if (isFocusNavigationKey) {\n              let candidateNodes = getItems().map((item) => item.ref.current!);\n              const prevItemKey = context.dir === 'rtl' ? 'ArrowRight' : 'ArrowLeft';\n              const prevKeys = [prevItemKey, 'ArrowUp', 'End'];\n              if (prevKeys.includes(event.key)) candidateNodes.reverse();\n              if (ARROW_KEYS.includes(event.key)) {\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = candidateNodes.slice(currentIndex + 1);\n              }\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n\n              // Prevent page scroll while navigating\n              event.preventDefault();\n            }\n          })}\n        />\n      </FocusGroupCollection.ItemSlot>\n    );\n  }\n);\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\nfunction focusFirst(candidates: HTMLElement[]) {\n  const previouslyFocusedElement = document.activeElement;\n  return candidates.some((candidate) => {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === previouslyFocusedElement) return true;\n    candidate.focus();\n    return document.activeElement !== previouslyFocusedElement;\n  });\n}\n\nfunction removeFromTabOrder(candidates: HTMLElement[]) {\n  candidates.forEach((candidate) => {\n    candidate.dataset.tabindex = candidate.getAttribute('tabindex') || '';\n    candidate.setAttribute('tabindex', '-1');\n  });\n  return () => {\n    candidates.forEach((candidate) => {\n      const prevTabIndex = candidate.dataset.tabindex as string;\n      candidate.setAttribute('tabindex', prevTabIndex);\n    });\n  };\n}\n\nfunction useResizeObserver(element: HTMLElement | null, onResize: () => void) {\n  const handleResize = useCallbackRef(onResize);\n  useLayoutEffect(() => {\n    let rAF = 0;\n    if (element) {\n      /**\n       * Resize Observer will throw an often benign error that says `ResizeObserver loop\n       * completed with undelivered notifications`. This means that ResizeObserver was not\n       * able to deliver all observations within a single animation frame, so we use\n       * `requestAnimationFrame` to ensure we don't deliver unnecessary observations.\n       * Further reading: https://github.com/WICG/resize-observer/issues/38\n       */\n      const resizeObserver = new ResizeObserver(() => {\n        cancelAnimationFrame(rAF);\n        rAF = window.requestAnimationFrame(handleResize);\n      });\n      resizeObserver.observe(element);\n      return () => {\n        window.cancelAnimationFrame(rAF);\n        resizeObserver.unobserve(element);\n      };\n    }\n  }, [element, handleResize]);\n}\n\nfunction getOpenState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nfunction makeTriggerId(baseId: string, value: string) {\n  return `${baseId}-trigger-${value}`;\n}\n\nfunction makeContentId(baseId: string, value: string) {\n  return `${baseId}-content-${value}`;\n}\n\nfunction whenMouse<E>(handler: React.PointerEventHandler<E>): React.PointerEventHandler<E> {\n  return (event) => (event.pointerType === 'mouse' ? handler(event) : undefined);\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = NavigationMenu;\nconst Sub = NavigationMenuSub;\nconst List = NavigationMenuList;\nconst Item = NavigationMenuItem;\nconst Trigger = NavigationMenuTrigger;\nconst Link = NavigationMenuLink;\nconst Indicator = NavigationMenuIndicator;\nconst Content = NavigationMenuContent;\nconst Viewport = NavigationMenuViewport;\n\nexport {\n  createNavigationMenuScope,\n  //\n  NavigationMenu,\n  NavigationMenuSub,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuContent,\n  NavigationMenuViewport,\n  //\n  Root,\n  Sub,\n  List,\n  Item,\n  Trigger,\n  Link,\n  Indicator,\n  Content,\n  Viewport,\n};\nexport type {\n  NavigationMenuProps,\n  NavigationMenuSubProps,\n  NavigationMenuListProps,\n  NavigationMenuItemProps,\n  NavigationMenuTriggerProps,\n  NavigationMenuLinkProps,\n  NavigationMenuIndicatorProps,\n  NavigationMenuContentProps,\n  NavigationMenuViewportProps,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,YAAuB;AACvB,uBAAqB;AACrB,2BAAmC;AACnC,uBAAqC;AACrC,6BAAuD;AACvD,0CAAqC;AACrC,gCAA6C;AAC7C,6BAA6B;AAC7B,4BAAyB;AACzB,sBAAsB;AACtB,8BAAiC;AACjC,qCAAiC;AACjC,gCAA4B;AAC5B,qCAAgC;AAChC,oCAA+B;AAC/B,8BAAyC;AA2LjC;AAhLR,IAAM,uBAAuB;AAE7B,IAAM,CAAC,YAAY,eAAe,qBAAqB,QAAI,0CAGzD,oBAAoB;AAEtB,IAAM,CAAC,sBAAsB,yBAAyB,+BAA+B,QACnF,0CAA4C,oBAAoB;AAGlE,IAAM,CAAC,6BAA6B,yBAAyB,QAAI;AAAA,EAC/D;AAAA,EACA,CAAC,uBAAuB,+BAA+B;AACzD;AA4BA,IAAM,CAAC,4BAA4B,wBAAwB,IACzD,4BAAwD,oBAAoB;AAE9E,IAAM,CAAC,yBAAyB,yBAAyB,IAAI,4BAE1D,oBAAoB;AAwBvB,IAAM,iBAAuB;AAAA,EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,MACpB,cAAc;AAAA,MACd;AAAA,MACA,GAAG;AAAA,IACL,IAAI;AACJ,UAAM,CAAC,gBAAgB,iBAAiB,IAAU,eAAuC,IAAI;AAC7F,UAAM,kBAAc,2CAAgB,cAAc,CAAC,SAAS,kBAAkB,IAAI,CAAC;AACnF,UAAM,gBAAY,qCAAa,GAAG;AAClC,UAAM,eAAqB,aAAO,CAAC;AACnC,UAAM,gBAAsB,aAAO,CAAC;AACpC,UAAM,oBAA0B,aAAO,CAAC;AACxC,UAAM,CAAC,eAAe,gBAAgB,IAAU,eAAS,IAAI;AAC7D,UAAM,CAAC,OAAO,QAAQ,QAAI,0DAAqB;AAAA,MAC7C,MAAM;AAAA,MACN,UAAU,CAACC,WAAU;AACnB,cAAM,SAASA,WAAU;AACzB,cAAM,uBAAuB,oBAAoB;AAEjD,YAAI,QAAQ;AACV,iBAAO,aAAa,kBAAkB,OAAO;AAC7C,cAAI,qBAAsB,kBAAiB,KAAK;AAAA,QAClD,OAAO;AACL,iBAAO,aAAa,kBAAkB,OAAO;AAC7C,4BAAkB,UAAU,OAAO;AAAA,YACjC,MAAM,iBAAiB,IAAI;AAAA,YAC3B;AAAA,UACF;AAAA,QACF;AAEA,wBAAgBA,MAAK;AAAA,MACvB;AAAA,MACA,aAAa,gBAAgB;AAAA,MAC7B,QAAQ;AAAA,IACV,CAAC;AAED,UAAM,kBAAwB,kBAAY,MAAM;AAC9C,aAAO,aAAa,cAAc,OAAO;AACzC,oBAAc,UAAU,OAAO,WAAW,MAAM,SAAS,EAAE,GAAG,GAAG;AAAA,IACnE,GAAG,CAAC,QAAQ,CAAC;AAEb,UAAM,aAAmB;AAAA,MACvB,CAAC,cAAsB;AACrB,eAAO,aAAa,cAAc,OAAO;AACzC,iBAAS,SAAS;AAAA,MACpB;AAAA,MACA,CAAC,QAAQ;AAAA,IACX;AAEA,UAAM,oBAA0B;AAAA,MAC9B,CAAC,cAAsB;AACrB,cAAM,aAAa,UAAU;AAC7B,YAAI,YAAY;AAGd,iBAAO,aAAa,cAAc,OAAO;AAAA,QAC3C,OAAO;AACL,uBAAa,UAAU,OAAO,WAAW,MAAM;AAC7C,mBAAO,aAAa,cAAc,OAAO;AACzC,qBAAS,SAAS;AAAA,UACpB,GAAG,aAAa;AAAA,QAClB;AAAA,MACF;AAAA,MACA,CAAC,OAAO,UAAU,aAAa;AAAA,IACjC;AAEA,IAAM,gBAAU,MAAM;AACpB,aAAO,MAAM;AACX,eAAO,aAAa,aAAa,OAAO;AACxC,eAAO,aAAa,cAAc,OAAO;AACzC,eAAO,aAAa,kBAAkB,OAAO;AAAA,MAC/C;AAAA,IACF,GAAG,CAAC,CAAC;AAEL,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO;AAAA,QACP,YAAY;AAAA,QACZ;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA,oBAAoB;AAAA,QACpB,gBAAgB,CAAC,cAAc;AAC7B,iBAAO,aAAa,aAAa,OAAO;AACxC,cAAI,cAAe,mBAAkB,SAAS;AAAA,cACzC,YAAW,SAAS;AAAA,QAC3B;AAAA,QACA,gBAAgB,MAAM;AACpB,iBAAO,aAAa,aAAa,OAAO;AACxC,0BAAgB;AAAA,QAClB;AAAA,QACA,gBAAgB,MAAM,OAAO,aAAa,cAAc,OAAO;AAAA,QAC/D,gBAAgB;AAAA,QAChB,cAAc,CAAC,cAAc;AAC3B,mBAAS,CAAC,cAAe,cAAc,YAAY,KAAK,SAAU;AAAA,QACpE;AAAA,QACA,eAAe,MAAM,SAAS,EAAE;AAAA,QAEhC;AAAA,UAAC,iCAAU;AAAA,UAAV;AAAA,YACC,cAAW;AAAA,YACX,oBAAkB;AAAA,YAClB,KAAK;AAAA,YACJ,GAAG;AAAA,YACJ,KAAK;AAAA;AAAA,QACP;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,eAAe,cAAc;AAM7B,IAAM,WAAW;AAajB,IAAM,oBAA0B;AAAA,EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA,cAAc;AAAA,MACd,GAAG;AAAA,IACL,IAAI;AACJ,UAAM,UAAU,yBAAyB,UAAU,qBAAqB;AACxE,UAAM,CAAC,OAAO,QAAQ,QAAI,0DAAqB;AAAA,MAC7C,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa,gBAAgB;AAAA,MAC7B,QAAQ;AAAA,IACV,CAAC;AAED,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO;AAAA,QACP,YAAY;AAAA,QACZ;AAAA,QACA,KAAK,QAAQ;AAAA,QACb;AAAA,QACA,oBAAoB,QAAQ;AAAA,QAC5B,gBAAgB,CAAC,cAAc,SAAS,SAAS;AAAA,QACjD,cAAc,CAAC,cAAc,SAAS,SAAS;AAAA,QAC/C,eAAe,MAAM,SAAS,EAAE;AAAA,QAEhC,sDAAC,iCAAU,KAAV,EAAc,oBAAkB,aAAc,GAAG,UAAU,KAAK,cAAc;AAAA;AAAA,IACjF;AAAA,EAEJ;AACF;AAEA,kBAAkB,cAAc;AAsBhC,IAAM,yBAAgE,CACpE,UACG;AACH,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,CAAC,UAAU,WAAW,IAAU,eAA+C,IAAI;AACzF,QAAM,CAAC,iBAAiB,kBAAkB,IAAU,eAAmC,oBAAI,IAAI,CAAC;AAChG,QAAM,CAAC,gBAAgB,iBAAiB,IAAU,eAAgC,IAAI;AAEtF,SACE;AAAA,IAAC;AAAA;AAAA,MACC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,mBAAe,uCAAY,KAAK;AAAA,MAChC,YAAQ,uBAAM;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA,kBAAkB;AAAA,MAClB;AAAA,MACA,wBAAwB;AAAA,MACxB,oBAAgB,8CAAe,cAAc;AAAA,MAC7C,oBAAgB,8CAAe,cAAc;AAAA,MAC7C,oBAAgB,8CAAe,cAAc;AAAA,MAC7C,oBAAgB,8CAAe,cAAc;AAAA,MAC7C,kBAAc,8CAAe,YAAY;AAAA,MACzC,mBAAe,8CAAe,aAAa;AAAA,MAC3C,yBAA+B,kBAAY,CAAC,cAAc,gBAAgB;AACxE,2BAAmB,CAAC,gBAAgB;AAClC,sBAAY,IAAI,cAAc,WAAW;AACzC,iBAAO,IAAI,IAAI,WAAW;AAAA,QAC5B,CAAC;AAAA,MACH,GAAG,CAAC,CAAC;AAAA,MACL,yBAA+B,kBAAY,CAAC,iBAAiB;AAC3D,2BAAmB,CAAC,gBAAgB;AAClC,cAAI,CAAC,YAAY,IAAI,YAAY,EAAG,QAAO;AAC3C,sBAAY,OAAO,YAAY;AAC/B,iBAAO,IAAI,IAAI,WAAW;AAAA,QAC5B,CAAC;AAAA,MACH,GAAG,CAAC,CAAC;AAAA,MAEL,sDAAC,WAAW,UAAX,EAAoB,OACnB,sDAAC,2BAAwB,OAAc,OAAO,iBAC3C,UACH,GACF;AAAA;AAAA,EACF;AAEJ;AAMA,IAAM,YAAY;AAMlB,IAAM,qBAA2B;AAAA,EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,uBAAuB,GAAG,UAAU,IAAI;AAChD,UAAM,UAAU,yBAAyB,WAAW,qBAAqB;AAEzE,UAAM,OACJ,4CAAC,iCAAU,IAAV,EAAa,oBAAkB,QAAQ,aAAc,GAAG,WAAW,KAAK,cAAc;AAGzF,WACE,4CAAC,iCAAU,KAAV,EAAc,OAAO,EAAE,UAAU,WAAW,GAAG,KAAK,QAAQ,wBAC3D,sDAAC,WAAW,MAAX,EAAgB,OAAO,uBACrB,kBAAQ,aAAa,4CAAC,cAAW,SAAO,MAAE,gBAAK,IAAgB,MAClE,GACF;AAAA,EAEJ;AACF;AAEA,mBAAmB,cAAc;AAMjC,IAAM,YAAY;AAgBlB,IAAM,CAAC,mCAAmC,4BAA4B,IACpE,4BAA4D,SAAS;AAQvE,IAAM,qBAA2B;AAAA,EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,uBAAuB,OAAO,WAAW,GAAG,UAAU,IAAI;AAClE,UAAM,gBAAY,uBAAM;AAGxB,UAAM,QAAQ,aAAa,aAAa;AACxC,UAAM,aAAmB,aAAqC,IAAI;AAClE,UAAM,aAAmB,aAAqC,IAAI;AAClE,UAAM,gBAAsB,aAA0B,IAAI;AAC1D,UAAM,4BAAkC,aAAO,MAAM;AAAA,IAAC,CAAC;AACvD,UAAM,oBAA0B,aAAO,KAAK;AAE5C,UAAM,qBAA2B,kBAAY,CAAC,OAAO,YAAY;AAC/D,UAAI,WAAW,SAAS;AACtB,kCAA0B,QAAQ;AAClC,cAAM,aAAa,sBAAsB,WAAW,OAAO;AAC3D,YAAI,WAAW,OAAQ,YAAW,SAAS,UAAU,aAAa,WAAW,QAAQ,CAAC;AAAA,MACxF;AAAA,IACF,GAAG,CAAC,CAAC;AAEL,UAAM,oBAA0B,kBAAY,MAAM;AAChD,UAAI,WAAW,SAAS;AACtB,cAAM,aAAa,sBAAsB,WAAW,OAAO;AAC3D,YAAI,WAAW,OAAQ,2BAA0B,UAAU,mBAAmB,UAAU;AAAA,MAC1F;AAAA,IACF,GAAG,CAAC,CAAC;AAEL,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,QACpB,uBAAuB;AAAA,QAEvB,sDAAC,iCAAU,IAAV,EAAc,GAAG,WAAW,KAAK,cAAc;AAAA;AAAA,IAClD;AAAA,EAEJ;AACF;AAEA,mBAAmB,cAAc;AAMjC,IAAM,eAAe;AAMrB,IAAM,wBAA8B,iBAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM,EAAE,uBAAuB,UAAU,GAAG,aAAa,IAAI;AAC7D,QAAM,UAAU,yBAAyB,cAAc,MAAM,qBAAqB;AAClF,QAAM,cAAc,6BAA6B,cAAc,MAAM,qBAAqB;AAC1F,QAAM,MAAY,aAAqC,IAAI;AAC3D,QAAM,mBAAe,2CAAgB,KAAK,YAAY,YAAY,YAAY;AAC9E,QAAM,YAAY,cAAc,QAAQ,QAAQ,YAAY,KAAK;AACjE,QAAM,YAAY,cAAc,QAAQ,QAAQ,YAAY,KAAK;AACjE,QAAM,0BAAgC,aAAO,KAAK;AAClD,QAAM,mBAAyB,aAAO,KAAK;AAC3C,QAAM,OAAO,YAAY,UAAU,QAAQ;AAE3C,SACE,4EACE;AAAA,gDAAC,WAAW,UAAX,EAAoB,OAAO,uBAAuB,OAAO,YAAY,OACpE,sDAAC,kBAAe,SAAO,MACrB;AAAA,MAAC,iCAAU;AAAA,MAAV;AAAA,QACC,IAAI;AAAA,QACJ;AAAA,QACA,iBAAe,WAAW,KAAK;AAAA,QAC/B,cAAY,aAAa,IAAI;AAAA,QAC7B,iBAAe;AAAA,QACf,iBAAe;AAAA,QACd,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,oBAAgB,uCAAqB,MAAM,gBAAgB,MAAM;AAC/D,2BAAiB,UAAU;AAC3B,sBAAY,kBAAkB,UAAU;AAAA,QAC1C,CAAC;AAAA,QACD,mBAAe;AAAA,UACb,MAAM;AAAA,UACN,UAAU,MAAM;AACd,gBACE,YACA,iBAAiB,WACjB,YAAY,kBAAkB,WAC9B,wBAAwB;AAExB;AACF,oBAAQ,eAAe,YAAY,KAAK;AACxC,oCAAwB,UAAU;AAAA,UACpC,CAAC;AAAA,QACH;AAAA,QACA,oBAAgB;AAAA,UACd,MAAM;AAAA,UACN,UAAU,MAAM;AACd,gBAAI,SAAU;AACd,oBAAQ,eAAe;AACvB,oCAAwB,UAAU;AAAA,UACpC,CAAC;AAAA,QACH;AAAA,QACA,aAAS,uCAAqB,MAAM,SAAS,MAAM;AACjD,kBAAQ,aAAa,YAAY,KAAK;AACtC,2BAAiB,UAAU;AAAA,QAC7B,CAAC;AAAA,QACD,eAAW,uCAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,gBAAM,mBAAmB,QAAQ,QAAQ,QAAQ,cAAc;AAC/D,gBAAM,WAAW,EAAE,YAAY,aAAa,UAAU,iBAAiB,EACrE,QAAQ,WACV;AACA,cAAI,QAAQ,MAAM,QAAQ,UAAU;AAClC,wBAAY,eAAe;AAE3B,kBAAM,eAAe;AAAA,UACvB;AAAA,QACF,CAAC;AAAA;AAAA,IACH,GACF,GACF;AAAA,IAGC,QACC,4EACE;AAAA;AAAA,QAAyB;AAAA,QAAxB;AAAA,UACC,eAAW;AAAA,UACX,UAAU;AAAA,UACV,KAAK,YAAY;AAAA,UACjB,SAAS,CAAC,UAAU;AAClB,kBAAM,UAAU,YAAY,WAAW;AACvC,kBAAM,qBAAqB,MAAM;AACjC,kBAAM,oBAAoB,uBAAuB,IAAI;AACrD,kBAAM,sBAAsB,SAAS,SAAS,kBAAkB;AAEhE,gBAAI,qBAAqB,CAAC,qBAAqB;AAC7C,0BAAY,kBAAkB,oBAAoB,UAAU,KAAK;AAAA,YACnE;AAAA,UACF;AAAA;AAAA,MACF;AAAA,MAGC,QAAQ,YAAY,4CAAC,UAAK,aAAW,WAAW;AAAA,OACnD;AAAA,KAEJ;AAEJ,CAAC;AAED,sBAAsB,cAAc;AAMpC,IAAM,YAAY;AAClB,IAAM,cAAc;AASpB,IAAM,qBAA2B;AAAA,EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,uBAAuB,QAAQ,UAAU,GAAG,UAAU,IAAI;AAElE,WACE,4CAAC,kBAAe,SAAO,MACrB;AAAA,MAAC,iCAAU;AAAA,MAAV;AAAA,QACC,eAAa,SAAS,KAAK;AAAA,QAC3B,gBAAc,SAAS,SAAS;AAAA,QAC/B,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,aAAS;AAAA,UACP,MAAM;AAAA,UACN,CAAC,UAAU;AACT,kBAAM,SAAS,MAAM;AACrB,kBAAM,kBAAkB,IAAI,YAAY,aAAa;AAAA,cACnD,SAAS;AAAA,cACT,YAAY;AAAA,YACd,CAAC;AACD,mBAAO,iBAAiB,aAAa,CAACC,WAAU,WAAWA,MAAK,GAAG,EAAE,MAAM,KAAK,CAAC;AACjF,oEAA4B,QAAQ,eAAe;AAEnD,gBAAI,CAAC,gBAAgB,oBAAoB,CAAC,MAAM,SAAS;AACvD,oBAAM,0BAA0B,IAAI,YAAY,sBAAsB;AAAA,gBACpE,SAAS;AAAA,gBACT,YAAY;AAAA,cACd,CAAC;AACD,sEAA4B,QAAQ,uBAAuB;AAAA,YAC7D;AAAA,UACF;AAAA,UACA,EAAE,0BAA0B,MAAM;AAAA,QACpC;AAAA;AAAA,IACF,GACF;AAAA,EAEJ;AACF;AAEA,mBAAmB,cAAc;AAMjC,IAAM,iBAAiB;AAWvB,IAAM,0BAAgC,iBAGpC,CAAC,OAAkD,iBAAiB;AACpE,QAAM,EAAE,YAAY,GAAG,eAAe,IAAI;AAC1C,QAAM,UAAU,yBAAyB,gBAAgB,MAAM,qBAAqB;AACpF,QAAM,YAAY,QAAQ,QAAQ,KAAK;AAEvC,SAAO,QAAQ,iBACX,iBAAAC,QAAS;AAAA,IACP,4CAAC,kCAAS,SAAS,cAAc,WAC/B,sDAAC,+BAA6B,GAAG,gBAAgB,KAAK,cAAc,GACtE;AAAA,IACA,QAAQ;AAAA,EACV,IACA;AACN,CAAC;AAED,wBAAwB,cAAc;AAKtC,IAAM,8BAAoC,iBAGxC,CAAC,OAAsD,iBAAiB;AACxE,QAAM,EAAE,uBAAuB,GAAG,eAAe,IAAI;AACrD,QAAM,UAAU,yBAAyB,gBAAgB,qBAAqB;AAC9E,QAAM,WAAW,cAAc,qBAAqB;AACpD,QAAM,CAAC,eAAe,gBAAgB,IAAU;AAAA,IAC9C;AAAA,EACF;AACA,QAAM,CAAC,UAAU,WAAW,IAAU,eAAkD,IAAI;AAC5F,QAAM,eAAe,QAAQ,gBAAgB;AAC7C,QAAM,YAAY,QAAQ,QAAQ,KAAK;AAEvC,EAAM,gBAAU,MAAM;AACpB,UAAM,QAAQ,SAAS;AACvB,UAAM,cAAc,MAAM,KAAK,CAAC,SAAS,KAAK,UAAU,QAAQ,KAAK,GAAG,IAAI;AAC5E,QAAI,YAAa,kBAAiB,WAAW;AAAA,EAC/C,GAAG,CAAC,UAAU,QAAQ,KAAK,CAAC;AAK5B,QAAM,uBAAuB,MAAM;AACjC,QAAI,eAAe;AACjB,kBAAY;AAAA,QACV,MAAM,eAAe,cAAc,cAAc,cAAc;AAAA,QAC/D,QAAQ,eAAe,cAAc,aAAa,cAAc;AAAA,MAClE,CAAC;AAAA,IACH;AAAA,EACF;AACA,oBAAkB,eAAe,oBAAoB;AACrD,oBAAkB,QAAQ,gBAAgB,oBAAoB;AAI9D,SAAO,WACL;AAAA,IAAC,iCAAU;AAAA,IAAV;AAAA,MACC,eAAW;AAAA,MACX,cAAY,YAAY,YAAY;AAAA,MACpC,oBAAkB,QAAQ;AAAA,MACzB,GAAG;AAAA,MACJ,KAAK;AAAA,MACL,OAAO;AAAA,QACL,UAAU;AAAA,QACV,GAAI,eACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO,SAAS,OAAO;AAAA,UACvB,WAAW,cAAc,SAAS,MAAM;AAAA,QAC1C,IACA;AAAA,UACE,KAAK;AAAA,UACL,QAAQ,SAAS,OAAO;AAAA,UACxB,WAAW,cAAc,SAAS,MAAM;AAAA,QAC1C;AAAA,QACJ,GAAG,eAAe;AAAA,MACpB;AAAA;AAAA,EACF,IACE;AACN,CAAC;AAMD,IAAM,eAAe;AAYrB,IAAM,wBAA8B,iBAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM,EAAE,YAAY,GAAG,aAAa,IAAI;AACxC,QAAM,UAAU,yBAAyB,cAAc,MAAM,qBAAqB;AAClF,QAAM,cAAc,6BAA6B,cAAc,MAAM,qBAAqB;AAC1F,QAAM,mBAAe,2CAAgB,YAAY,YAAY,YAAY;AACzE,QAAM,OAAO,YAAY,UAAU,QAAQ;AAE3C,QAAM,cAAc;AAAA,IAClB,OAAO,YAAY;AAAA,IACnB,YAAY,YAAY;AAAA,IACxB,eAAe,YAAY;AAAA,IAC3B,mBAAmB,YAAY;AAAA,IAC/B,uBAAuB,YAAY;AAAA,IACnC,oBAAoB,YAAY;AAAA,IAChC,GAAG;AAAA,EACL;AAEA,SAAO,CAAC,QAAQ,WACd,4CAAC,kCAAS,SAAS,cAAc,MAC/B;AAAA,IAAC;AAAA;AAAA,MACC,cAAY,aAAa,IAAI;AAAA,MAC5B,GAAG;AAAA,MACJ,KAAK;AAAA,MACL,oBAAgB,uCAAqB,MAAM,gBAAgB,QAAQ,cAAc;AAAA,MACjF,oBAAgB;AAAA,QACd,MAAM;AAAA,QACN,UAAU,QAAQ,cAAc;AAAA,MAClC;AAAA,MACA,OAAO;AAAA;AAAA,QAEL,eAAe,CAAC,QAAQ,QAAQ,aAAa,SAAS;AAAA,QACtD,GAAG,YAAY;AAAA,MACjB;AAAA;AAAA,EACF,GACF,IAEA,4CAAC,0BAAuB,YAAyB,GAAG,aAAa,KAAK,cAAc;AAExF,CAAC;AAED,sBAAsB,cAAc;AAapC,IAAM,yBAA+B,iBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,UAAU,yBAAyB,cAAc,MAAM,qBAAqB;AAClF,QAAM,EAAE,yBAAyB,wBAAwB,IAAI;AAE7D,sDAAgB,MAAM;AACpB,4BAAwB,MAAM,OAAO;AAAA,MACnC,KAAK;AAAA,MACL,GAAG;AAAA,IACL,CAAC;AAAA,EACH,GAAG,CAAC,OAAO,cAAc,uBAAuB,CAAC;AAEjD,sDAAgB,MAAM;AACpB,WAAO,MAAM,wBAAwB,MAAM,KAAK;AAAA,EAClD,GAAG,CAAC,MAAM,OAAO,uBAAuB,CAAC;AAGzC,SAAO;AACT,CAAC;AAID,IAAM,uBAAuB;AAkB7B,IAAM,4BAAkC,iBAGtC,CAAC,OAAoD,iBAAiB;AACtE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,UAAU,yBAAyB,cAAc,qBAAqB;AAC5E,QAAM,MAAY,aAAyC,IAAI;AAC/D,QAAM,mBAAe,2CAAgB,KAAK,YAAY;AACtD,QAAM,YAAY,cAAc,QAAQ,QAAQ,KAAK;AACrD,QAAM,YAAY,cAAc,QAAQ,QAAQ,KAAK;AACrD,QAAM,WAAW,cAAc,qBAAqB;AACpD,QAAM,yBAA+B,aAA+B,IAAI;AAExE,QAAM,EAAE,cAAc,IAAI;AAE1B,EAAM,gBAAU,MAAM;AACpB,UAAM,UAAU,IAAI;AAGpB,QAAI,QAAQ,cAAc,SAAS;AACjC,YAAM,cAAc,MAAM;AACxB,sBAAc;AACd,2BAAmB;AACnB,YAAI,QAAQ,SAAS,SAAS,aAAa,EAAG,YAAW,SAAS,MAAM;AAAA,MAC1E;AACA,cAAQ,iBAAiB,sBAAsB,WAAW;AAC1D,aAAO,MAAM,QAAQ,oBAAoB,sBAAsB,WAAW;AAAA,IAC5E;AAAA,EACF,GAAG,CAAC,QAAQ,YAAY,MAAM,OAAO,YAAY,eAAe,kBAAkB,CAAC;AAEnF,QAAM,kBAAwB,cAAQ,MAAM;AAC1C,UAAM,QAAQ,SAAS;AACvB,UAAM,SAAS,MAAM,IAAI,CAAC,SAAS,KAAK,KAAK;AAC7C,QAAI,QAAQ,QAAQ,MAAO,QAAO,QAAQ;AAC1C,UAAM,QAAQ,OAAO,QAAQ,QAAQ,KAAK;AAC1C,UAAM,YAAY,OAAO,QAAQ,QAAQ,aAAa;AACtD,UAAM,aAAa,UAAU,QAAQ;AACrC,UAAM,cAAc,cAAc,OAAO,QAAQ,KAAK;AAItD,QAAI,CAAC,cAAc,CAAC,YAAa,QAAO,uBAAuB;AAE/D,UAAM,aAAa,MAAM;AAEvB,UAAI,UAAU,WAAW;AAEvB,YAAI,cAAc,cAAc,GAAI,QAAO,QAAQ,YAAY,aAAa;AAE5E,YAAI,eAAe,UAAU,GAAI,QAAO,QAAQ,YAAY,aAAa;AAAA,MAC3E;AAGA,aAAO;AAAA,IACT,GAAG;AAEH,2BAAuB,UAAU;AACjC,WAAO;AAAA,EACT,GAAG,CAAC,QAAQ,eAAe,QAAQ,OAAO,QAAQ,KAAK,UAAU,KAAK,CAAC;AAEvE,SACE,4CAAC,cAAW,SAAO,MACjB;AAAA,IAAC;AAAA;AAAA,MACC,IAAI;AAAA,MACJ,mBAAiB;AAAA,MACjB,eAAa;AAAA,MACb,oBAAkB,QAAQ;AAAA,MACzB,GAAG;AAAA,MACJ,KAAK;AAAA,MACL,6BAA6B;AAAA,MAC7B,WAAW,MAAM;AACf,cAAM,0BAA0B,IAAI,MAAM,sBAAsB;AAAA,UAC9D,SAAS;AAAA,UACT,YAAY;AAAA,QACd,CAAC;AACD,YAAI,SAAS,cAAc,uBAAuB;AAAA,MACpD;AAAA,MACA,oBAAgB,uCAAqB,MAAM,gBAAgB,CAAC,UAAU;AACpE,8BAAsB;AACtB,cAAM,SAAS,MAAM;AAErB,YAAI,QAAQ,oBAAoB,SAAS,MAAM,EAAG,OAAM,eAAe;AAAA,MACzE,CAAC;AAAA,MACD,0BAAsB,uCAAqB,MAAM,sBAAsB,CAAC,UAAU;AAChF,cAAM,SAAS,MAAM;AACrB,cAAM,YAAY,SAAS,EAAE,KAAK,CAAC,SAAS,KAAK,IAAI,SAAS,SAAS,MAAM,CAAC;AAC9E,cAAM,iBAAiB,QAAQ,cAAc,QAAQ,UAAU,SAAS,MAAM;AAC9E,YAAI,aAAa,kBAAkB,CAAC,QAAQ,WAAY,OAAM,eAAe;AAAA,MAC/E,CAAC;AAAA,MACD,eAAW,uCAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,cAAM,YAAY,MAAM,UAAU,MAAM,WAAW,MAAM;AACzD,cAAM,WAAW,MAAM,QAAQ,SAAS,CAAC;AACzC,YAAI,UAAU;AACZ,gBAAM,aAAa,sBAAsB,MAAM,aAAa;AAC5D,gBAAM,iBAAiB,SAAS;AAChC,gBAAM,QAAQ,WAAW,UAAU,CAAC,cAAc,cAAc,cAAc;AAC9E,gBAAM,oBAAoB,MAAM;AAChC,gBAAM,iBAAiB,oBACnB,WAAW,MAAM,GAAG,KAAK,EAAE,QAAQ,IACnC,WAAW,MAAM,QAAQ,GAAG,WAAW,MAAM;AAEjD,cAAI,WAAW,cAAc,GAAG;AAE9B,kBAAM,eAAe;AAAA,UACvB,OAAO;AAIL,0BAAc,SAAS,MAAM;AAAA,UAC/B;AAAA,QACF;AAAA,MACF,CAAC;AAAA,MACD,qBAAiB,uCAAqB,MAAM,iBAAiB,CAAC,WAAW;AAGvE,0BAAkB,UAAU;AAAA,MAC9B,CAAC;AAAA;AAAA,EACH,GACF;AAEJ,CAAC;AAMD,IAAM,gBAAgB;AAYtB,IAAM,yBAA+B,iBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,EAAE,YAAY,GAAG,cAAc,IAAI;AACzC,QAAM,UAAU,yBAAyB,eAAe,MAAM,qBAAqB;AACnF,QAAM,OAAO,QAAQ,QAAQ,KAAK;AAElC,SACE,4CAAC,kCAAS,SAAS,cAAc,MAC/B,sDAAC,8BAA4B,GAAG,eAAe,KAAK,cAAc,GACpE;AAEJ,CAAC;AAED,uBAAuB,cAAc;AAOrC,IAAM,6BAAmC,iBAGvC,CAAC,OAAqD,iBAAiB;AACvE,QAAM,EAAE,uBAAuB,UAAU,GAAG,kBAAkB,IAAI;AAClE,QAAM,UAAU,yBAAyB,eAAe,qBAAqB;AAC7E,QAAM,mBAAe,2CAAgB,cAAc,QAAQ,gBAAgB;AAC3E,QAAM,yBAAyB;AAAA,IAC7B;AAAA,IACA,MAAM;AAAA,EACR;AACA,QAAM,CAAC,MAAM,OAAO,IAAU,eAAmD,IAAI;AACrF,QAAM,CAAC,SAAS,UAAU,IAAU,eAA8C,IAAI;AACtF,QAAM,gBAAgB,OAAO,MAAM,QAAQ,OAAO;AAClD,QAAM,iBAAiB,OAAO,MAAM,SAAS,OAAO;AACpD,QAAM,OAAO,QAAQ,QAAQ,KAAK;AAGlC,QAAM,qBAAqB,OAAO,QAAQ,QAAQ,QAAQ;AAQ1D,QAAM,mBAAmB,MAAM;AAC7B,QAAI,QAAS,SAAQ,EAAE,OAAO,QAAQ,aAAa,QAAQ,QAAQ,aAAa,CAAC;AAAA,EACnF;AACA,oBAAkB,SAAS,gBAAgB;AAE3C,SACE;AAAA,IAAC,iCAAU;AAAA,IAAV;AAAA,MACC,cAAY,aAAa,IAAI;AAAA,MAC7B,oBAAkB,QAAQ;AAAA,MACzB,GAAG;AAAA,MACJ,KAAK;AAAA,MACL,OAAO;AAAA;AAAA,QAEL,eAAe,CAAC,QAAQ,QAAQ,aAAa,SAAS;AAAA,QACtD,CAAC,wCAA+C,GAAG;AAAA,QACnD,CAAC,yCAAgD,GAAG;AAAA,QACpD,GAAG,kBAAkB;AAAA,MACvB;AAAA,MACA,oBAAgB,uCAAqB,MAAM,gBAAgB,QAAQ,cAAc;AAAA,MACjF,oBAAgB,uCAAqB,MAAM,gBAAgB,UAAU,QAAQ,cAAc,CAAC;AAAA,MAE3F,gBAAM,KAAK,uBAAuB,KAAK,EAAE,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,YAAY,GAAGC,OAAM,CAAC,MAAM;AACxF,cAAM,WAAW,uBAAuB;AACxC,eACE,4CAAC,kCAAqB,SAAS,cAAc,UAC3C;AAAA,UAAC;AAAA;AAAA,YACE,GAAGA;AAAA,YACJ,SAAK,uCAAY,KAAK,CAAC,SAAS;AAG9B,kBAAI,YAAY,KAAM,YAAW,IAAI;AAAA,YACvC,CAAC;AAAA;AAAA,QACH,KARa,KASf;AAAA,MAEJ,CAAC;AAAA;AAAA,EACH;AAEJ,CAAC;AAID,IAAM,mBAAmB;AAKzB,IAAM,aAAmB;AAAA,EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM,EAAE,uBAAuB,GAAG,WAAW,IAAI;AACjD,UAAM,UAAU,yBAAyB,kBAAkB,qBAAqB;AAEhF,WACE,4CAAC,qBAAqB,UAArB,EAA8B,OAAO,uBACpC,sDAAC,qBAAqB,MAArB,EAA0B,OAAO,uBAChC,sDAAC,iCAAU,KAAV,EAAc,KAAK,QAAQ,KAAM,GAAG,YAAY,KAAK,cAAc,GACtE,GACF;AAAA,EAEJ;AACF;AAIA,IAAM,aAAa,CAAC,cAAc,aAAa,WAAW,WAAW;AACrE,IAAM,wBAAwB;AAK9B,IAAM,iBAAuB;AAAA,EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,EAAE,uBAAuB,GAAG,WAAW,IAAI;AACjD,UAAM,WAAW,wBAAwB,qBAAqB;AAC9D,UAAM,UAAU,yBAAyB,uBAAuB,qBAAqB;AAErF,WACE,4CAAC,qBAAqB,UAArB,EAA8B,OAAO,uBACpC;AAAA,MAAC,iCAAU;AAAA,MAAV;AAAA,QACE,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,eAAW,uCAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,gBAAM,uBAAuB,CAAC,QAAQ,OAAO,GAAG,UAAU,EAAE,SAAS,MAAM,GAAG;AAC9E,cAAI,sBAAsB;AACxB,gBAAI,iBAAiB,SAAS,EAAE,IAAI,CAAC,SAAS,KAAK,IAAI,OAAQ;AAC/D,kBAAM,cAAc,QAAQ,QAAQ,QAAQ,eAAe;AAC3D,kBAAM,WAAW,CAAC,aAAa,WAAW,KAAK;AAC/C,gBAAI,SAAS,SAAS,MAAM,GAAG,EAAG,gBAAe,QAAQ;AACzD,gBAAI,WAAW,SAAS,MAAM,GAAG,GAAG;AAClC,oBAAM,eAAe,eAAe,QAAQ,MAAM,aAAa;AAC/D,+BAAiB,eAAe,MAAM,eAAe,CAAC;AAAA,YACxD;AAKA,uBAAW,MAAM,WAAW,cAAc,CAAC;AAG3C,kBAAM,eAAe;AAAA,UACvB;AAAA,QACF,CAAC;AAAA;AAAA,IACH,GACF;AAAA,EAEJ;AACF;AAYA,SAAS,sBAAsB,WAAwB;AACrD,QAAM,QAAuB,CAAC;AAC9B,QAAM,SAAS,SAAS,iBAAiB,WAAW,WAAW,cAAc;AAAA,IAC3E,YAAY,CAAC,SAAc;AACzB,YAAM,gBAAgB,KAAK,YAAY,WAAW,KAAK,SAAS;AAChE,UAAI,KAAK,YAAY,KAAK,UAAU,cAAe,QAAO,WAAW;AAIrE,aAAO,KAAK,YAAY,IAAI,WAAW,gBAAgB,WAAW;AAAA,IACpE;AAAA,EACF,CAAC;AACD,SAAO,OAAO,SAAS,EAAG,OAAM,KAAK,OAAO,WAA0B;AAGtE,SAAO;AACT;AAEA,SAAS,WAAW,YAA2B;AAC7C,QAAM,2BAA2B,SAAS;AAC1C,SAAO,WAAW,KAAK,CAAC,cAAc;AAEpC,QAAI,cAAc,yBAA0B,QAAO;AACnD,cAAU,MAAM;AAChB,WAAO,SAAS,kBAAkB;AAAA,EACpC,CAAC;AACH;AAEA,SAAS,mBAAmB,YAA2B;AACrD,aAAW,QAAQ,CAAC,cAAc;AAChC,cAAU,QAAQ,WAAW,UAAU,aAAa,UAAU,KAAK;AACnE,cAAU,aAAa,YAAY,IAAI;AAAA,EACzC,CAAC;AACD,SAAO,MAAM;AACX,eAAW,QAAQ,CAAC,cAAc;AAChC,YAAM,eAAe,UAAU,QAAQ;AACvC,gBAAU,aAAa,YAAY,YAAY;AAAA,IACjD,CAAC;AAAA,EACH;AACF;AAEA,SAAS,kBAAkB,SAA6B,UAAsB;AAC5E,QAAM,mBAAe,8CAAe,QAAQ;AAC5C,sDAAgB,MAAM;AACpB,QAAI,MAAM;AACV,QAAI,SAAS;AAQX,YAAM,iBAAiB,IAAI,eAAe,MAAM;AAC9C,6BAAqB,GAAG;AACxB,cAAM,OAAO,sBAAsB,YAAY;AAAA,MACjD,CAAC;AACD,qBAAe,QAAQ,OAAO;AAC9B,aAAO,MAAM;AACX,eAAO,qBAAqB,GAAG;AAC/B,uBAAe,UAAU,OAAO;AAAA,MAClC;AAAA,IACF;AAAA,EACF,GAAG,CAAC,SAAS,YAAY,CAAC;AAC5B;AAEA,SAAS,aAAa,MAAe;AACnC,SAAO,OAAO,SAAS;AACzB;AAEA,SAAS,cAAc,QAAgB,OAAe;AACpD,SAAO,GAAG,MAAM,YAAY,KAAK;AACnC;AAEA,SAAS,cAAc,QAAgB,OAAe;AACpD,SAAO,GAAG,MAAM,YAAY,KAAK;AACnC;AAEA,SAAS,UAAa,SAAqE;AACzF,SAAO,CAAC,UAAW,MAAM,gBAAgB,UAAU,QAAQ,KAAK,IAAI;AACtE;AAIA,IAAMC,QAAO;AACb,IAAM,MAAM;AACZ,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,OAAO;AACb,IAAM,YAAY;AAClB,IAAM,UAAU;AAChB,IAAM,WAAW;", "names": ["Root", "value", "event", "ReactDOM", "props", "Root"]}