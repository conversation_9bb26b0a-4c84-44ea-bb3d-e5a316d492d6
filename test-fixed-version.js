const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔧 测试修复后的打包版本...');

// 检查修复后的版本
const fixedBasePath = 'dist-release-fixed-final';
const appPath = path.join(fixedBasePath, 'win-unpacked', '游戏修改器盒子.exe');
const portablePath = path.join(fixedBasePath, '游戏修改器盒子-便携版.exe');

console.log('\n📁 检查修复版本文件:');
console.log(`  - 解包版: ${fs.existsSync(appPath) ? '✅' : '❌'} ${appPath}`);
console.log(`  - 便携版: ${fs.existsSync(portablePath) ? '✅' : '❌'} ${portablePath}`);

// 检查关键修复点
const resourcesPath = path.join(fixedBasePath, 'win-unpacked', 'resources');
const chromiumPath = path.join(resourcesPath, 'chromium', 'chrome.exe');
const scraperPath = path.join(resourcesPath, 'scraper');

console.log('\n🔧 检查修复的关键组件:');
console.log(`  - Chromium浏览器: ${fs.existsSync(chromiumPath) ? '✅' : '❌'}`);
console.log(`  - 爬虫脚本目录: ${fs.existsSync(scraperPath) ? '✅' : '❌'}`);

if (fs.existsSync(scraperPath)) {
  const scraperFiles = fs.readdirSync(scraperPath);
  console.log(`  - 爬虫脚本文件: ${scraperFiles.join(', ')}`);
  
  // 检查每个脚本文件
  const requiredScripts = ['scraper.js', 'detail-scraper.js', 'search-scraper.js', 'translator.js'];
  requiredScripts.forEach(script => {
    const scriptPath = path.join(scraperPath, script);
    console.log(`    ${script}: ${fs.existsSync(scriptPath) ? '✅' : '❌'}`);
  });
}

console.log('\n🚀 修复内容总结:');
console.log('  1. ✅ 修复了子进程启动方式 (fork → spawn + Node.js路径)');
console.log('  2. ✅ 修复了打包环境中的脚本路径问题');
console.log('  3. ✅ 添加了ELECTRON_RUN_AS_NODE环境变量');
console.log('  4. ✅ 统一了开发环境和生产环境的子进程启动逻辑');

console.log('\n🧪 测试建议:');
console.log('  1. 启动解包版应用程序');
console.log('  2. 测试首页修改器列表显示');
console.log('  3. 测试搜索功能 (输入游戏名称)');
console.log('  4. 测试下载选项获取 (点击修改器的"立即下载")');
console.log('  5. 测试游戏名称翻译功能');
console.log('  6. 测试便携版启动');

console.log('\n💡 如果仍有问题，请检查:');
console.log('  - 应用程序控制台输出 (F12开发者工具)');
console.log('  - 子进程启动日志');
console.log('  - 网络连接状态');

console.log('\n🎯 预期修复的问题:');
console.log('  ❌ 搜索功能失效 → ✅ 应该正常工作');
console.log('  ❌ 下载选项获取失效 → ✅ 应该正常工作');
console.log('  ❌ 便携版无法启动 → ✅ 应该正常启动');

console.log('\n📍 测试路径:');
console.log(`  解包版: ${appPath}`);
console.log(`  便携版: ${portablePath}`);
