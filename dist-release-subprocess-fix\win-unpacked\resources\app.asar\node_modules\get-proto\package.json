{"name": "get-proto", "version": "1.0.1", "description": "Robustly get the [[Prototype]] of an object", "main": "index.js", "exports": {".": "./index.js", "./Reflect.getPrototypeOf": "./Reflect.getPrototypeOf.js", "./Object.getPrototypeOf": "./Object.getPrototypeOf.js", "./package.json": "./package.json"}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/get-proto.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/ljharb/get-proto#readme", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.2", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/tape": "^5.8.0", "auto-changelog": "^2.5.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "testling": {"files": "test/index.js"}}