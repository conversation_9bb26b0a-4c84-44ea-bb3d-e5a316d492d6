{"name": "input-otp", "version": "1.4.2", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "description": "One-time password input component for React.", "license": "MIT", "homepage": "https://input-otp.rodz.dev/", "repository": {"type": "git", "url": "git+https://github.com/guilhermerodz/input-otp.git", "directory": "packages/input-otp"}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "exports": {".": {"types": "./dist/index.d.ts", "module": "./dist/index.mjs", "import": "./dist/index.mjs", "require": "./dist/index.js", "default": "./dist/index.mjs"}, "./package.json": "./package.json"}, "devDependencies": {"@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc"}}