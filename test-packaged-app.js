// 测试打包后应用的脚本
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔍 测试打包后的应用程序...');

// 查找打包后的可执行文件
const possiblePaths = [
  'dist-release-fixed/win-unpacked/游戏修改器盒子.exe',
  'dist-release-working/win-unpacked/游戏修改器盒子.exe',
  'dist-release-working/游戏修改器盒子-便携版.exe',
  'dist-release-working/游戏修改器盒子-1.0.0-x64.exe',
  'dist-release-final/win-unpacked/游戏修改器盒子.exe',
  'dist-release-final/游戏修改器盒子-便携版.exe',
  'dist-release-final/游戏修改器盒子-1.0.0-x64.exe',
  'dist-release-new/win-unpacked/游戏修改器盒子.exe',
  'dist-release-new/游戏修改器盒子-便携版.exe',
  'dist-release-new/游戏修改器盒子-1.0.0-x64.exe',
  'dist-release/win-unpacked/游戏修改器盒子.exe',
  'dist-release/游戏修改器盒子-便携版.exe',
  'dist-release/游戏修改器盒子-1.0.0-x64.exe',
  'release/win-unpacked/游戏修改器盒子.exe',
  'release/游戏修改器盒子-便携版.exe',
  'release/游戏修改器盒子-1.0.0-x64.exe'
];

let executablePath = null;
for (const testPath of possiblePaths) {
  if (fs.existsSync(testPath)) {
    executablePath = testPath;
    console.log(`✅ 找到可执行文件: ${executablePath}`);
    break;
  }
}

if (!executablePath) {
  console.error('❌ 未找到打包后的可执行文件');
  console.log('检查的路径:');
  possiblePaths.forEach(p => console.log(`  - ${p}`));
  process.exit(1);
}

// 启动应用程序并捕获输出
console.log('🚀 启动应用程序...');
const app = spawn(executablePath, [], {
  stdio: ['pipe', 'pipe', 'pipe'],
  cwd: process.cwd()
});

let output = '';
let errorOutput = '';

app.stdout.on('data', (data) => {
  const text = data.toString();
  output += text;
  console.log(`[STDOUT] ${text.trim()}`);
});

app.stderr.on('data', (data) => {
  const text = data.toString();
  errorOutput += text;
  console.error(`[STDERR] ${text.trim()}`);
});

app.on('close', (code) => {
  console.log(`\n📊 应用程序退出，代码: ${code}`);
  
  if (output.includes('收到爬虫数据') && output.includes('数据项数量')) {
    console.log('✅ 应用程序功能正常 - 成功抓取到数据');
  } else if (output.includes('找到 Chromium') || output.includes('✅ 已创建爬虫子进程')) {
    console.log('⚠️  应用程序部分正常 - 进程启动成功但可能数据抓取有问题');
  } else {
    console.log('❌ 应用程序可能存在问题');
  }
  
  console.log('\n📝 完整输出日志:');
  console.log('--- STDOUT ---');
  console.log(output);
  console.log('--- STDERR ---');
  console.log(errorOutput);
});

app.on('error', (error) => {
  console.error(`❌ 启动应用程序失败: ${error.message}`);
});

// 60秒后自动关闭
setTimeout(() => {
  console.log('⏰ 测试超时，关闭应用程序...');
  app.kill('SIGTERM');
  setTimeout(() => {
    app.kill('SIGKILL');
  }, 5000);
}, 60000);
