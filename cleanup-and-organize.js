const fs = require('fs');
const path = require('path');

console.log('🧹 清理和整理打包文件...');

// 重命名成功的版本
const successDir = 'dist-release-fixed';
const finalDir = 'release-final';

if (fs.existsSync(successDir)) {
  if (fs.existsSync(finalDir)) {
    console.log(`删除现有的 ${finalDir} 目录...`);
    fs.rmSync(finalDir, { recursive: true, force: true });
  }
  
  console.log(`重命名 ${successDir} 为 ${finalDir}...`);
  fs.renameSync(successDir, finalDir);
  console.log('✅ 成功版本已重命名为 release-final');
} else {
  console.log('❌ 找不到成功的打包版本');
}

// 清理失败的版本
const failedDirs = [
  'dist-release',
  'dist-release-new', 
  'dist-release-final',
  'dist-release-working',
  'dist-release-v2'
];

failedDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    try {
      console.log(`删除失败的版本: ${dir}...`);
      fs.rmSync(dir, { recursive: true, force: true });
      console.log(`✅ 已删除 ${dir}`);
    } catch (error) {
      console.log(`⚠️  无法删除 ${dir}: ${error.message}`);
    }
  }
});

console.log('\n📦 最终的打包文件位置:');
if (fs.existsSync(finalDir)) {
  console.log(`✅ 主程序: ${finalDir}/win-unpacked/游戏修改器盒子.exe`);
  console.log(`✅ 完整目录: ${finalDir}/win-unpacked/`);
} else {
  console.log('❌ 找不到最终的打包文件');
}

console.log('\n🎉 清理完成！');
