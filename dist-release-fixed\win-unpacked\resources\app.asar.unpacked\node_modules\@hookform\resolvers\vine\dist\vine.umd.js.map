{"version": 3, "file": "vine.umd.js", "sources": ["../src/vine.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { SimpleErrorReporter, errors } from '@vinejs/vine';\nimport { FieldError, FieldErrors, appendErrors } from 'react-hook-form';\nimport type { Resolver } from './types';\n\nconst parseErrorSchema = (\n  vineErrors: SimpleErrorReporter['errors'],\n  validateAllFieldCriteria: boolean,\n) => {\n  const schemaErrors: Record<string, FieldError> = {};\n\n  for (; vineErrors.length; ) {\n    const error = vineErrors[0];\n    const path = error.field;\n\n    if (!(path in schemaErrors)) {\n      schemaErrors[path] = { message: error.message, type: error.rule };\n    }\n\n    if (validateAllFieldCriteria) {\n      const { types } = schemaErrors[path];\n      const messages = types && types[error.rule];\n\n      schemaErrors[path] = appendErrors(\n        path,\n        validateAllFieldCriteria,\n        schemaErrors,\n        error.rule,\n        messages ? [...(messages as string[]), error.message] : error.message,\n      ) as FieldError;\n    }\n\n    vineErrors.shift();\n  }\n\n  return schemaErrors;\n};\n\nexport const vineResolver: Resolver =\n  (schema, schemaOptions, resolverOptions = {}) =>\n  async (values, _, options) => {\n    try {\n      const data = await schema.validate(values, schemaOptions);\n\n      options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n      return {\n        errors: {} as FieldErrors,\n        values: resolverOptions.raw ? values : data,\n      };\n    } catch (error: any) {\n      if (error instanceof errors.E_VALIDATION_ERROR) {\n        return {\n          values: {},\n          errors: toNestErrors(\n            parseErrorSchema(\n              error.messages,\n              !options.shouldUseNativeValidation &&\n                options.criteriaMode === 'all',\n            ),\n            options,\n          ),\n        };\n      }\n\n      throw error;\n    }\n  };\n"], "names": ["parseErrorSchema", "vineErrors", "validateAllFieldCriteria", "schemaErrors", "length", "error", "path", "field", "message", "type", "rule", "types", "messages", "appendErrors", "concat", "shift", "schema", "schemaOptions", "resolverOptions", "values", "_", "options", "Promise", "resolve", "validate", "then", "data", "shouldUseNativeValidation", "validateFieldsNatively", "errors", "raw", "_catch", "E_VALIDATION_ERROR", "toNestErrors", "criteriaMode", "e", "reject"], "mappings": "yaAKA,IAAMA,EAAmB,SACvBC,EACAC,GAIA,IAFA,IAAMC,EAA2C,CAAA,EAE1CF,EAAWG,QAAU,CAC1B,IAAMC,EAAQJ,EAAW,GACnBK,EAAOD,EAAME,MAMnB,GAJMD,KAAQH,IACZA,EAAaG,GAAQ,CAAEE,QAASH,EAAMG,QAASC,KAAMJ,EAAMK,OAGzDR,EAA0B,CAC5B,IAAQS,EAAUR,EAAaG,GAAvBK,MACFC,EAAWD,GAASA,EAAMN,EAAMK,MAEtCP,EAAaG,GAAQO,eACnBP,EACAJ,EACAC,EACAE,EAAMK,KACNE,EAAQ,GAAAE,OAAQF,EAAuBP,CAAAA,EAAMG,UAAWH,EAAMG,QAElE,CAEAP,EAAWc,OACb,CAEA,OAAOZ,CACT,iBAGE,SAACa,EAAQC,EAAeC,GACjBC,YADiBD,IAAAA,IAAAA,EAAkB,CAAE,GACrCC,SAAAA,EAAQC,EAAGC,GAAW,IAAA,OAAAC,QAAAC,gCACvBD,QAAAC,QACiBP,EAAOQ,SAASL,EAAQF,IAAcQ,KAAnDC,SAAAA,GAIN,OAFAL,EAAQM,2BAA6BC,EAAAA,uBAAuB,CAAE,EAAEP,GAEzD,CACLQ,OAAQ,GACRV,OAAQD,EAAgBY,IAAMX,EAASO,EACvC,4DATuBK,GAU1B,SAAQ1B,GACP,GAAIA,aAAiBwB,EAAMA,OAACG,mBAC1B,MAAO,CACLb,OAAQ,CAAA,EACRU,OAAQI,EAAYA,aAClBjC,EACEK,EAAMO,UACLS,EAAQM,2BACkB,QAAzBN,EAAQa,cAEZb,IAKN,MAAMhB,CACR,GACF,CAAC,MAAA8B,GAAA,OAAAb,QAAAc,OAAAD,EAAA,CAAA,CAAA"}