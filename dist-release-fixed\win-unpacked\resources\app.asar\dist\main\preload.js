/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/main/preload.ts":
/*!*****************************!*\
  !*** ./src/main/preload.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst electron_1 = __webpack_require__(/*! electron */ \"electron\");\n// 暴露安全的API给渲染进程\nelectron_1.contextBridge.exposeInMainWorld('electron', {\n    // 窗口控制\n    windowMinimize: () => electron_1.ipcRenderer.send('window-minimize'),\n    windowMaximize: () => electron_1.ipcRenderer.send('window-maximize'),\n    windowClose: () => electron_1.ipcRenderer.send('window-close'),\n    // 添加shell API用于打开外部链接\n    shell: {\n        openExternal: (url) => {\n            // 白名单URL\n            const validUrls = [\n                'https://flingtrainer.com/',\n                'https://flingtrainer.com',\n                'https://www.baidu.com/'\n            ];\n            // 检查URL是否以白名单中的任一URL开头\n            const isValidUrl = validUrls.some(validUrl => url === validUrl || url.startsWith(`${validUrl}/`));\n            if (isValidUrl) {\n                electron_1.shell.openExternal(url);\n            }\n        }\n    },\n    // 数据通信\n    ipcRenderer: {\n        // 请求修改器数据\n        send: (channel, ...args) => {\n            // 白名单通道\n            const validSendChannels = [\n                'renderer-ready',\n                'request-trainer-data',\n                'request-trainer-download-options',\n                'download-file',\n                'get-downloads',\n                'clear-completed-downloads',\n                'delete-download',\n                'set-download-folder',\n                'get-download-folder',\n                'search-trainers',\n                'open-external-url'\n            ];\n            if (validSendChannels.includes(channel)) {\n                electron_1.ipcRenderer.send(channel, ...args);\n            }\n        },\n        // 调用主进程方法并等待结果\n        invoke: async (channel, ...args) => {\n            // 白名单通道\n            const validInvokeChannels = [\n                'get-download-options',\n                'get-download-folder',\n                'get-app-info',\n                'get-system-info',\n                'translate-game-name'\n            ];\n            if (validInvokeChannels.includes(channel)) {\n                return await electron_1.ipcRenderer.invoke(channel, ...args);\n            }\n            return null;\n        },\n        // 监听来自主进程的消息\n        on: (channel, func) => {\n            // 白名单通道\n            const validReceiveChannels = [\n                'update-trainer-data',\n                'update-trainer-data-error',\n                'trainer-download-options-response',\n                'download-update',\n                'downloads-list-update',\n                'download-folder',\n                'download-folder-updated',\n                'search-results-response',\n                'download-completed'\n            ];\n            if (validReceiveChannels.includes(channel)) {\n                // 包装函数以避免原始事件对象泄漏到渲染进程\n                const subscription = (event, ...args) => func(...args);\n                electron_1.ipcRenderer.on(channel, subscription);\n                // 返回一个清理函数\n                return () => {\n                    electron_1.ipcRenderer.removeListener(channel, subscription);\n                };\n            }\n            return () => { }; // 返回空函数\n        },\n        // 移除监听器\n        removeListener: (channel, func) => {\n            // 白名单通道\n            const validReceiveChannels = [\n                'update-trainer-data',\n                'update-trainer-data-error',\n                'trainer-download-options-response',\n                'download-update',\n                'downloads-list-update',\n                'download-folder',\n                'download-folder-updated',\n                'search-results-response'\n            ];\n            if (validReceiveChannels.includes(channel)) {\n                electron_1.ipcRenderer.removeListener(channel, func);\n            }\n        }\n    }\n});\n\n\n//# sourceURL=webpack://game-modifier-box-reborn/./src/main/preload.ts?");

/***/ }),

/***/ "electron":
/*!***************************!*\
  !*** external "electron" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("electron");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./src/main/preload.ts");
/******/ 	
/******/ })()
;