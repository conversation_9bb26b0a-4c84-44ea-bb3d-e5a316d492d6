{"version": 3, "file": "emscripten-types.js", "sourceRoot": "", "sources": ["../ts/emscripten-types.ts"], "names": [], "mappings": ";AAAA,6EAA6E;AAC7E,+DAA+D;AAC/D,+DAA+D;AAC/D,oEAAoE;AACpE,kEAAkE;AAClE,EAAE;AACF,gGAAgG;AAChG,EAAE;AACF,iFAAiF;AACjF,0BAA0B;AAC1B,iFAAiF;AACjF,kBAAkB", "sourcesContent": ["// This is a subset of the Emscripten type definitions from @types/emscripten\n// Project: http://kripken.github.io/emscripten-site/index.html\n// Definitions by: <PERSON><PERSON> <https://github.com/zak<PERSON>>\n//                 <PERSON><PERSON><PERSON> <https://github.com/periklis>\n// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped\n//\n// quickjs-emscripten doesn't use the full EmscriptenModule type from @types/emscripten because:\n//\n// - the upstream types define many properties that don't exist on our module due\n//   to our build settings\n// - some upstream types reference web-only ambient types like WebGL stuff, which\n//   we don't use.\n\nimport {\n  BorrowedHeapCharPointer,\n  JSContextPointer,\n  JSRuntimePointer,\n  JSValueConstPointer,\n  JSValuePointer,\n  OwnedHeapCharPointer,\n} from \"./types-ffi\"\n\ndeclare namespace Emscripten {\n  interface FileSystemType {}\n  type EnvironmentType = \"WEB\" | \"NODE\" | \"SHELL\" | \"WORKER\"\n  type ValueType = \"number\" | \"string\" | \"array\" | \"boolean\"\n  type TypeCompatibleWithC = number | string | any[] | boolean\n\n  type WebAssemblyImports = Array<{\n    name: string\n    kind: string\n  }>\n\n  type WebAssemblyExports = Array<{\n    module: string\n    name: string\n    kind: string\n  }>\n\n  interface CCallOpts {\n    async?: boolean\n  }\n}\n\n/**\n * Typings for the features we use to interface with our Emscripten build of\n * QuickJS.\n */\ninterface EmscriptenModule {\n  // No longer needed:\n  // addFunction(fn: Function, type: string): number\n  // removeFunction(pointer: number): void\n\n  /**\n   * Write JS `str` to HeapChar pointer.\n   * https://emscripten.org/docs/api_reference/preamble.js.html#stringToUTF8\n   */\n  stringToUTF8(str: string, outPtr: OwnedHeapCharPointer, maxBytesToRead?: number): void\n  /**\n   * HeapChar to JS string.\n   * https://emscripten.org/docs/api_reference/preamble.js.html#UTF8ToString\n   */\n  UTF8ToString(ptr: BorrowedHeapCharPointer, maxBytesToRead?: number): string\n  lengthBytesUTF8(str: string): number\n\n  _malloc(size: number): number\n  _free(ptr: number): void\n  cwrap(\n    ident: string,\n    returnType: Emscripten.ValueType | null,\n    argTypes: Emscripten.ValueType[],\n    opts?: Emscripten.CCallOpts\n  ): (...args: any[]) => any\n\n  // USE_TYPED_ARRAYS == 2\n  HEAP8: Int8Array\n  HEAP16: Int16Array\n  HEAP32: Int32Array\n  HEAPU8: Uint8Array\n  HEAPU16: Uint16Array\n  HEAPU32: Uint32Array\n  HEAPF32: Float32Array\n  HEAPF64: Float64Array\n\n  TOTAL_STACK: number\n  TOTAL_MEMORY: number\n  FAST_MEMORY: number\n}\n\n// This isn't the real return type of handleAsync, but it's better to treat it this way.\ndeclare const AsyncifySleepReturnValue: unique symbol\n/** @private */\nexport type AsyncifySleepResult<T> = T & typeof AsyncifySleepReturnValue\n\n/**\n * Allows us to optionally suspend the Emscripten runtime to wait for a promise.\n * https://emscripten.org/docs/porting/asyncify.html#ways-to-use-async-apis-in-older-engines\n * ```\n * EM_JS(int, do_fetch, (), {\n *   return Asyncify.handleSleep(function (wakeUp) {\n *     out(\"waiting for a fetch\");\n *     fetch(\"a.html\").then(function (response) {\n *       out(\"got the fetch response\");\n *       // (normally you would do something with the fetch here)\n *       wakeUp(42);\n *     });\n *   });\n * });\n * ```\n * @private\n */\nexport interface Asyncify {\n  handleSleep<T>(maybeAsyncFn: (wakeUp: (result: T) => void) => void): AsyncifySleepResult<T>\n  // Because this one requires a promise, it's going to be less efficient than\n  // the callback system. Plus it seems like we'd need to use SyncPromise to\n  // avoid suspending.\n  // handleAsync<T>(asyncFn: () => T | Promise<T>): AsyncifySleepResult<T>\n}\n\n/**\n * @private\n */\nexport interface EmscriptenModuleCallbacks {\n  callFunction: (\n    asyncify: Asyncify | undefined,\n    ctx: JSContextPointer,\n    this_ptr: JSValueConstPointer,\n    argc: number,\n    argv: JSValueConstPointer,\n    fn_id: number\n  ) => JSValuePointer | AsyncifySleepResult<JSValuePointer>\n\n  loadModuleSource: (\n    asyncify: Asyncify | undefined,\n    rt: JSRuntimePointer,\n    ctx: JSContextPointer,\n    module_name: string\n  ) => BorrowedHeapCharPointer | AsyncifySleepResult<BorrowedHeapCharPointer>\n\n  normalizeModule: (\n    asyncify: Asyncify | undefined,\n    rt: JSRuntimePointer,\n    ctx: JSContextPointer,\n    module_base_name: string,\n    module_name: string\n  ) => BorrowedHeapCharPointer | AsyncifySleepResult<BorrowedHeapCharPointer>\n\n  shouldInterrupt: (\n    asyncify: Asyncify | undefined,\n    rt: JSRuntimePointer\n  ) => 0 | 1 | AsyncifySleepResult<0 | 1>\n}\n\nexport interface QuickJSEmscriptenModule extends EmscriptenModule {\n  type: \"sync\"\n  callbacks: EmscriptenModuleCallbacks\n}\n\nexport interface QuickJSAsyncEmscriptenModule extends EmscriptenModule {\n  /** @todo Implement this field */\n  type: \"async\"\n  callbacks: EmscriptenModuleCallbacks\n}\n\nexport type EitherModule = QuickJSEmscriptenModule | QuickJSAsyncEmscriptenModule\n\nexport interface EmscriptenModuleLoader<T extends EmscriptenModule> {\n  (): Promise<T>\n}\n"]}