{"version": 3, "sources": ["../src/blur-tables.js"], "names": ["mulTable", "shgTable"], "mappings": ";;;;;;AAAO,IAAMA,QAAQ,GAAG,CACtB,CADsB,EACnB,EADmB,EACf,EADe,EACX,EADW,EACP,GADO,EACF,EADE,EACE,EADF,EACM,EADN,EACU,GADV,EACe,EADf,EACmB,GADnB,EACwB,EADxB,EAC4B,GAD5B,EACiC,EADjC,EACqC,EADrC,EACyC,GADzC,EAC8C,GAD9C,EACmD,GADnD,EAEtB,CAFsB,EAEnB,GAFmB,EAEd,EAFc,EAEV,EAFU,EAEN,EAFM,EAEF,GAFE,EAEG,GAFH,EAEQ,GAFR,EAEa,GAFb,EAEkB,EAFlB,EAEsB,EAFtB,EAE0B,GAF1B,EAE+B,GAF/B,EAEoC,GAFpC,EAEyC,GAFzC,EAE8C,GAF9C,EAEmD,GAFnD,EAGtB,GAHsB,EAGjB,GAHiB,EAGZ,GAHY,EAGP,GAHO,EAGF,GAHE,EAGG,CAHH,EAGM,GAHN,EAGW,EAHX,EAGe,GAHf,EAGoB,GAHpB,EAGyB,GAHzB,EAG8B,GAH9B,EAGmC,GAHnC,EAGwC,GAHxC,EAG6C,GAH7C,EAGkD,GAHlD,EAItB,EAJsB,EAIlB,GAJkB,EAIb,EAJa,EAIT,GAJS,EAIJ,GAJI,EAIC,GAJD,EAIM,GAJN,EAIW,EAJX,EAIe,GAJf,EAIoB,CAJpB,EAIuB,GAJvB,EAI4B,GAJ5B,EAIiC,GAJjC,EAIsC,GAJtC,EAI2C,GAJ3C,EAIgD,GAJhD,EAKtB,GALsB,EAKjB,GALiB,EAKZ,GALY,EAKP,GALO,EAKF,GALE,EAKG,EALH,EAKO,GALP,EAKY,GALZ,EAKiB,EALjB,EAKqB,EALrB,EAKyB,GALzB,EAK8B,GAL9B,EAKmC,EALnC,EAKuC,EALvC,EAK2C,EAL3C,EAK+C,GAL/C,EAKoD,GALpD,EAMtB,GANsB,EAMjB,CANiB,EAMd,GANc,EAMT,GANS,EAMJ,EANI,EAMA,GANA,EAMK,GANL,EAMU,GANV,EAMe,GANf,EAMoB,EANpB,EAMwB,GANxB,EAM6B,GAN7B,EAMkC,GANlC,EAMuC,GANvC,EAM4C,GAN5C,EAMiD,EANjD,EAMqD,EANrD,EAOtB,EAPsB,EAOlB,EAPkB,EAOd,EAPc,EAOV,GAPU,EAOL,GAPK,EAOA,GAPA,EAOK,EAPL,EAOS,GAPT,EAOc,GAPd,EAOmB,EAPnB,EAOuB,GAPvB,EAO4B,EAP5B,EAOgC,GAPhC,EAOqC,CAPrC,EAOwC,EAPxC,EAO4C,GAP5C,EAOiD,EAPjD,EAOqD,EAPrD,EAQtB,GARsB,EAQjB,GARiB,EAQZ,GARY,EAQP,EARO,EAQH,EARG,EAQC,EARD,EAQK,EARL,EAQS,EART,EAQa,EARb,EAQiB,GARjB,EAQsB,GARtB,EAQ2B,GAR3B,EAQgC,GARhC,EAQqC,GARrC,EAQ0C,EAR1C,EAQ8C,EAR9C,EAQkD,GARlD,EAStB,GATsB,EASjB,GATiB,EASZ,GATY,EASP,EATO,EASH,GATG,EASE,GATF,EASO,GATP,EASY,EATZ,EASgB,GAThB,EASqB,GATrB,EAS0B,EAT1B,EAS8B,GAT9B,EASmC,GATnC,EASwC,EATxC,EAS4C,EAT5C,EASgD,GAThD,EAUtB,GAVsB,EAUjB,GAViB,EAUZ,EAVY,EAUR,EAVQ,EAUJ,EAVI,EAUA,EAVA,EAUI,GAVJ,EAUS,GAVT,EAUc,GAVd,EAUmB,GAVnB,EAUwB,GAVxB,EAU6B,GAV7B,EAUkC,GAVlC,EAUuC,EAVvC,EAU2C,EAV3C,EAU+C,EAV/C,EAUmD,EAVnD,EAWtB,EAXsB,EAWlB,GAXkB,EAWb,GAXa,EAWR,GAXQ,EAWH,EAXG,EAWC,GAXD,EAWM,GAXN,EAWW,GAXX,EAWgB,EAXhB,EAWoB,EAXpB,EAWwB,GAXxB,EAW6B,GAX7B,EAWkC,GAXlC,EAWuC,EAXvC,EAW2C,GAX3C,EAWgD,GAXhD,EAWqD,EAXrD,EAYtB,GAZsB,EAYjB,GAZiB,EAYZ,GAZY,EAYP,GAZO,EAYF,EAZE,EAYE,GAZF,EAYO,GAZP,EAYY,GAZZ,EAYiB,GAZjB,EAYsB,EAZtB,EAY0B,GAZ1B,EAY+B,GAZ/B,EAYoC,GAZpC,EAYyC,GAZzC,EAY8C,GAZ9C,EAYmD,GAZnD,EAatB,GAbsB,EAajB,GAbiB,EAaZ,GAbY,EAaP,GAbO,EAaF,GAbE,EAaG,GAbH,EAaQ,GAbR,EAaa,EAbb,EAaiB,GAbjB,EAasB,EAbtB,EAa0B,EAb1B,EAa8B,GAb9B,EAamC,GAbnC,EAawC,GAbxC,EAa6C,GAb7C,EAakD,GAblD,EActB,GAdsB,EAcjB,GAdiB,EAcZ,GAdY,EAcP,GAdO,EAcF,EAdE,EAcE,EAdF,EAcM,GAdN,EAcW,GAdX,EAcgB,EAdhB,EAcoB,GAdpB,EAcyB,GAdzB,EAc8B,CAd9B,EAciC,EAdjC,EAcqC,GAdrC,EAc0C,EAd1C,EAc8C,GAd9C,EAcmD,GAdnD,EAetB,EAfsB,EAelB,EAfkB,EAed,GAfc,EAeT,EAfS,EAeL,GAfK,EAeA,EAfA,EAeI,GAfJ,EAeS,GAfT,EAec,EAfd,EAekB,GAflB,EAeuB,GAfvB,EAe4B,GAf5B,EAeiC,GAfjC,EAesC,EAftC,EAe0C,GAf1C,EAe+C,GAf/C,EAeoD,GAfpD,EAgBtB,EAhBsB,EAgBlB,GAhBkB,EAgBb,EAhBa,EAgBT,GAhBS,EAgBJ,CAhBI,CAAjB;;AAmBA,IAAMC,QAAQ,GAAG,CACtB,CADsB,EACnB,CADmB,EAChB,EADgB,EACZ,EADY,EACR,EADQ,EACJ,EADI,EACA,EADA,EACI,EADJ,EACQ,EADR,EACY,EADZ,EACgB,EADhB,EACoB,EADpB,EACwB,EADxB,EAC4B,EAD5B,EACgC,EADhC,EACoC,EADpC,EACwC,EADxC,EAC4C,EAD5C,EACgD,EADhD,EACoD,EADpD,EAEtB,EAFsB,EAElB,EAFkB,EAEd,EAFc,EAEV,EAFU,EAEN,EAFM,EAEF,EAFE,EAEE,EAFF,EAEM,EAFN,EAEU,EAFV,EAEc,EAFd,EAEkB,EAFlB,EAEsB,EAFtB,EAE0B,EAF1B,EAE8B,EAF9B,EAEkC,EAFlC,EAEsC,EAFtC,EAE0C,EAF1C,EAE8C,EAF9C,EAEkD,EAFlD,EAGtB,EAHsB,EAGlB,EAHkB,EAGd,EAHc,EAGV,EAHU,EAGN,EAHM,EAGF,EAHE,EAGE,EAHF,EAGM,EAHN,EAGU,EAHV,EAGc,EAHd,EAGkB,EAHlB,EAGsB,EAHtB,EAG0B,EAH1B,EAG8B,EAH9B,EAGkC,EAHlC,EAGsC,EAHtC,EAG0C,EAH1C,EAG8C,EAH9C,EAGkD,EAHlD,EAItB,EAJsB,EAIlB,EAJkB,EAId,EAJc,EAIV,EAJU,EAIN,EAJM,EAIF,EAJE,EAIE,EAJF,EAIM,EAJN,EAIU,EAJV,EAIc,EAJd,EAIkB,EAJlB,EAIsB,EAJtB,EAI0B,EAJ1B,EAI8B,EAJ9B,EAIkC,EAJlC,EAIsC,EAJtC,EAI0C,EAJ1C,EAI8C,EAJ9C,EAIkD,EAJlD,EAKtB,EALsB,EAKlB,EALkB,EAKd,EALc,EAKV,EALU,EAKN,EALM,EAKF,EALE,EAKE,EALF,EAKM,EALN,EAKU,EALV,EAKc,EALd,EAKkB,EALlB,EAKsB,EALtB,EAK0B,EAL1B,EAK8B,EAL9B,EAKkC,EALlC,EAKsC,EALtC,EAK0C,EAL1C,EAK8C,EAL9C,EAKkD,EALlD,EAMtB,EANsB,EAMlB,EANkB,EAMd,EANc,EAMV,EANU,EAMN,EANM,EAMF,EANE,EAME,EANF,EAMM,EANN,EAMU,EANV,EAMc,EANd,EAMkB,EANlB,EAMsB,EANtB,EAM0B,EAN1B,EAM8B,EAN9B,EAMkC,EANlC,EAMsC,EANtC,EAM0C,EAN1C,EAM8C,EAN9C,EAMkD,EANlD,EAOtB,EAPsB,EAOlB,EAPkB,EAOd,EAPc,EAOV,EAPU,EAON,EAPM,EAOF,EAPE,EAOE,EAPF,EAOM,EAPN,EAOU,EAPV,EAOc,EAPd,EAOkB,EAPlB,EAOsB,EAPtB,EAO0B,EAP1B,EAO8B,EAP9B,EAOkC,EAPlC,EAOsC,EAPtC,EAO0C,EAP1C,EAO8C,EAP9C,EAOkD,EAPlD,EAQtB,EARsB,EAQlB,EARkB,EAQd,EARc,EAQV,EARU,EAQN,EARM,EAQF,EARE,EAQE,EARF,EAQM,EARN,EAQU,EARV,EAQc,EARd,EAQkB,EARlB,EAQsB,EARtB,EAQ0B,EAR1B,EAQ8B,EAR9B,EAQkC,EARlC,EAQsC,EARtC,EAQ0C,EAR1C,EAQ8C,EAR9C,EAQkD,EARlD,EAStB,EATsB,EASlB,EATkB,EASd,EATc,EASV,EATU,EASN,EATM,EASF,EATE,EASE,EATF,EASM,EATN,EASU,EATV,EASc,EATd,EASkB,EATlB,EASsB,EATtB,EAS0B,EAT1B,EAS8B,EAT9B,EASkC,EATlC,EASsC,EATtC,EAS0C,EAT1C,EAS8C,EAT9C,EASkD,EATlD,EAUtB,EAVsB,EAUlB,EAVkB,EAUd,EAVc,EAUV,EAVU,EAUN,EAVM,EAUF,EAVE,EAUE,EAVF,EAUM,EAVN,EAUU,EAVV,EAUc,EAVd,EAUkB,EAVlB,EAUsB,EAVtB,EAU0B,EAV1B,EAU8B,EAV9B,EAUkC,EAVlC,EAUsC,EAVtC,EAU0C,EAV1C,EAU8C,EAV9C,EAUkD,EAVlD,EAWtB,EAXsB,EAWlB,EAXkB,EAWd,EAXc,EAWV,EAXU,EAWN,EAXM,EAWF,EAXE,EAWE,EAXF,EAWM,EAXN,EAWU,EAXV,EAWc,EAXd,EAWkB,EAXlB,EAWsB,EAXtB,EAW0B,EAX1B,EAW8B,EAX9B,EAWkC,EAXlC,EAWsC,EAXtC,EAW0C,EAX1C,EAW8C,EAX9C,EAWkD,EAXlD,EAYtB,EAZsB,EAYlB,EAZkB,EAYd,EAZc,EAYV,EAZU,EAYN,EAZM,EAYF,EAZE,EAYE,EAZF,EAYM,EAZN,EAYU,EAZV,EAYc,EAZd,EAYkB,EAZlB,EAYsB,EAZtB,EAY0B,EAZ1B,EAY8B,EAZ9B,EAYkC,EAZlC,EAYsC,EAZtC,EAY0C,EAZ1C,EAY8C,EAZ9C,EAYkD,EAZlD,EAatB,EAbsB,EAalB,EAbkB,EAad,EAbc,EAaV,EAbU,EAaN,EAbM,EAaF,EAbE,EAaE,EAbF,EAaM,EAbN,EAaU,EAbV,EAac,EAbd,EAakB,EAblB,EAasB,EAbtB,EAa0B,EAb1B,EAa8B,EAb9B,EAakC,EAblC,EAasC,EAbtC,EAa0C,EAb1C,EAa8C,EAb9C,EAakD,EAblD,EActB,EAdsB,EAclB,EAdkB,EAcd,EAdc,EAcV,EAdU,EAcN,EAdM,EAcF,EAdE,EAcE,EAdF,EAcM,EAdN,EAcU,EAdV,CAAjB", "sourcesContent": ["export const mulTable = [\n  1, 57, 41, 21, 203, 34, 97, 73, 227, 91, 149, 62, 105, 45, 39, 137, 241, 107,\n  3, 173, 39, 71, 65, 238, 219, 101, 187, 87, 81, 151, 141, 133, 249, 117, 221,\n  209, 197, 187, 177, 169, 5, 153, 73, 139, 133, 127, 243, 233, 223, 107, 103,\n  99, 191, 23, 177, 171, 165, 159, 77, 149, 9, 139, 135, 131, 253, 245, 119,\n  231, 224, 109, 211, 103, 25, 195, 189, 23, 45, 175, 171, 83, 81, 79, 155, 151,\n  147, 9, 141, 137, 67, 131, 129, 251, 123, 30, 235, 115, 113, 221, 217, 53, 13,\n  51, 50, 49, 193, 189, 185, 91, 179, 175, 43, 169, 83, 163, 5, 79, 155, 19, 75,\n  147, 145, 143, 35, 69, 17, 67, 33, 65, 255, 251, 247, 243, 239, 59, 29, 229,\n  113, 111, 219, 27, 213, 105, 207, 51, 201, 199, 49, 193, 191, 47, 93, 183,\n  181, 179, 11, 87, 43, 85, 167, 165, 163, 161, 159, 157, 155, 77, 19, 75, 37,\n  73, 145, 143, 141, 35, 138, 137, 135, 67, 33, 131, 129, 255, 63, 250, 247, 61,\n  121, 239, 237, 117, 29, 229, 227, 225, 111, 55, 109, 216, 213, 211, 209, 207,\n  205, 203, 201, 199, 197, 195, 193, 48, 190, 47, 93, 185, 183, 181, 179, 178,\n  176, 175, 173, 171, 85, 21, 167, 165, 41, 163, 161, 5, 79, 157, 78, 154, 153,\n  19, 75, 149, 74, 147, 73, 144, 143, 71, 141, 140, 139, 137, 17, 135, 134, 133,\n  66, 131, 65, 129, 1,\n];\n\nexport const shgTable = [\n  0, 9, 10, 10, 14, 12, 14, 14, 16, 15, 16, 15, 16, 15, 15, 17, 18, 17, 12, 18,\n  16, 17, 17, 19, 19, 18, 19, 18, 18, 19, 19, 19, 20, 19, 20, 20, 20, 20, 20,\n  20, 15, 20, 19, 20, 20, 20, 21, 21, 21, 20, 20, 20, 21, 18, 21, 21, 21, 21,\n  20, 21, 17, 21, 21, 21, 22, 22, 21, 22, 22, 21, 22, 21, 19, 22, 22, 19, 20,\n  22, 22, 21, 21, 21, 22, 22, 22, 18, 22, 22, 21, 22, 22, 23, 22, 20, 23, 22,\n  22, 23, 23, 21, 19, 21, 21, 21, 23, 23, 23, 22, 23, 23, 21, 23, 22, 23, 18,\n  22, 23, 20, 22, 23, 23, 23, 21, 22, 20, 22, 21, 22, 24, 24, 24, 24, 24, 22,\n  21, 24, 23, 23, 24, 21, 24, 23, 24, 22, 24, 24, 22, 24, 24, 22, 23, 24, 24,\n  24, 20, 23, 22, 23, 24, 24, 24, 24, 24, 24, 24, 23, 21, 23, 22, 23, 24, 24,\n  24, 22, 24, 24, 24, 23, 22, 24, 24, 25, 23, 25, 25, 23, 24, 25, 25, 24, 22,\n  25, 25, 25, 24, 23, 24, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 23,\n  25, 23, 24, 25, 25, 25, 25, 25, 25, 25, 25, 25, 24, 22, 25, 25, 23, 25, 25,\n  20, 24, 25, 24, 25, 25, 22, 24, 25, 24, 25, 24, 25, 25, 24, 25, 25, 25, 25,\n  22, 25, 25, 25, 24, 25, 24, 25, 18,\n];\n"], "file": "blur-tables.js"}