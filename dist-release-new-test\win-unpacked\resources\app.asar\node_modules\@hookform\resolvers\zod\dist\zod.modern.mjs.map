{"version": 3, "file": "zod.modern.mjs", "sources": ["../src/zod.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { FieldError, FieldErrors, appendErrors } from 'react-hook-form';\nimport { ZodError, z } from 'zod';\nimport type { Resolver } from './types';\n\nconst isZodError = (error: any): error is ZodError =>\n  Array.isArray(error?.errors);\n\nconst parseErrorSchema = (\n  zodErrors: z.ZodIssue[],\n  validateAllFieldCriteria: boolean,\n) => {\n  const errors: Record<string, FieldError> = {};\n  for (; zodErrors.length; ) {\n    const error = zodErrors[0];\n    const { code, message, path } = error;\n    const _path = path.join('.');\n\n    if (!errors[_path]) {\n      if ('unionErrors' in error) {\n        const unionError = error.unionErrors[0].errors[0];\n\n        errors[_path] = {\n          message: unionError.message,\n          type: unionError.code,\n        };\n      } else {\n        errors[_path] = { message, type: code };\n      }\n    }\n\n    if ('unionErrors' in error) {\n      error.unionErrors.forEach((unionError) =>\n        unionError.errors.forEach((e) => zodErrors.push(e)),\n      );\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = errors[_path].types;\n      const messages = types && types[error.code];\n\n      errors[_path] = appendErrors(\n        _path,\n        validateAllFieldCriteria,\n        errors,\n        code,\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message)\n          : error.message,\n      ) as FieldError;\n    }\n\n    zodErrors.shift();\n  }\n\n  return errors;\n};\n\nexport const zodResolver: Resolver =\n  (schema, schemaOptions, resolverOptions = {}) =>\n  async (values, _, options) => {\n    try {\n      const data = await schema[\n        resolverOptions.mode === 'sync' ? 'parse' : 'parseAsync'\n      ](values, schemaOptions);\n\n      options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n      return {\n        errors: {} as FieldErrors,\n        values: resolverOptions.raw ? values : data,\n      };\n    } catch (error: any) {\n      if (isZodError(error)) {\n        return {\n          values: {},\n          errors: toNestErrors(\n            parseErrorSchema(\n              error.errors,\n              !options.shouldUseNativeValidation &&\n                options.criteriaMode === 'all',\n            ),\n            options,\n          ),\n        };\n      }\n\n      throw error;\n    }\n  };\n"], "names": ["parseErrorSchema", "zodErrors", "validateAllFieldCriteria", "errors", "length", "error", "code", "message", "path", "_path", "join", "unionError", "unionErrors", "type", "for<PERSON>ach", "e", "push", "types", "messages", "appendErrors", "concat", "shift", "zodResolver", "schema", "schemaOptions", "resolverOptions", "async", "values", "_", "options", "data", "mode", "shouldUseNativeValidation", "validateFieldsNatively", "raw", "Array", "isArray", "isZodError", "toNestErrors", "criteriaMode"], "mappings": "8HAKA,MAGMA,EAAmBA,CACvBC,EACAC,KAEA,MAAMC,EAAqC,CAAA,EAC3C,KAAOF,EAAUG,QAAU,CACzB,MAAMC,EAAQJ,EAAU,IAClBK,KAAEA,EAAIC,QAAEA,EAAOC,KAAEA,GAASH,EAC1BI,EAAQD,EAAKE,KAAK,KAExB,IAAKP,EAAOM,GACV,GAAI,gBAAiBJ,EAAO,CAC1B,MAAMM,EAAaN,EAAMO,YAAY,GAAGT,OAAO,GAE/CA,EAAOM,GAAS,CACdF,QAASI,EAAWJ,QACpBM,KAAMF,EAAWL,KAErB,MACEH,EAAOM,GAAS,CAAEF,UAASM,KAAMP,GAUrC,GANI,gBAAiBD,GACnBA,EAAMO,YAAYE,QAASH,GACzBA,EAAWR,OAAOW,QAASC,GAAMd,EAAUe,KAAKD,KAIhDb,EAA0B,CAC5B,MAAMe,EAAQd,EAAOM,GAAOQ,MACtBC,EAAWD,GAASA,EAAMZ,EAAMC,MAEtCH,EAAOM,GAASU,EACdV,EACAP,EACAC,EACAG,EACAY,EACK,GAAgBE,OAAOF,EAAsBb,EAAME,SACpDF,EAAME,QAEd,CAEAN,EAAUoB,OACZ,CAEA,OAAOlB,GAGImB,EACXA,CAACC,EAAQC,EAAeC,EAAkB,KAC1CC,MAAOC,EAAQC,EAAGC,KAChB,IACE,MAAMC,QAAaP,EACQ,SAAzBE,EAAgBM,KAAkB,QAAU,cAC5CJ,EAAQH,GAIV,OAFAK,EAAQG,2BAA6BC,EAAuB,CAAE,EAAEJ,GAEzD,CACL1B,OAAQ,CAAA,EACRwB,OAAQF,EAAgBS,IAAMP,EAASG,EAE3C,CAAE,MAAOzB,GACP,GApEcA,IAClB8B,MAAMC,QAAQ/B,MAAAA,OAAAA,EAAAA,EAAOF,QAmEbkC,CAAWhC,GACb,MAAO,CACLsB,OAAQ,GACRxB,OAAQmC,EACNtC,EACEK,EAAMF,QACL0B,EAAQG,2BACkB,QAAzBH,EAAQU,cAEZV,IAKN,MAAMxB,CACR"}