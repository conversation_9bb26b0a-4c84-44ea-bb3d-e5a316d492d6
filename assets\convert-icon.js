// 此脚本用于将SVG图标转换为ICO文件
// 需要安装以下依赖：
// npm install svg2img jimp to-ico --save-dev

const fs = require('fs');
const path = require('path');
const svg2img = require('svg2img');
const Jimp = require('jimp');
const toIco = require('to-ico');

const SVG_PATH = path.join(__dirname, 'icon.svg');
const OUTPUT_PATH = path.join(__dirname, 'icon.ico');

// 定义图标尺寸
const sizes = [16, 24, 32, 48, 64, 128, 256];

console.log('开始转换SVG到ICO...');

// 读取SVG文件
const svgContent = fs.readFileSync(SVG_PATH, 'utf8');

// 创建不同尺寸的PNG图像
const createImages = async () => {
  const pngBuffers = [];
  
  for (const size of sizes) {
    console.log(`生成 ${size}x${size} 尺寸的图像...`);
    
    // 将SVG转换为PNG
    const pngBuffer = await new Promise((resolve, reject) => {
      svg2img(svgContent, { width: size, height: size }, (err, buffer) => {
        if (err) reject(err);
        else resolve(buffer);
      });
    });
    
    try {
      // 使用Jimp处理图像
      const image = await Jimp.read(pngBuffer);
      const processedBuffer = await image.getBufferAsync(Jimp.MIME_PNG);
      pngBuffers.push(processedBuffer);
    } catch (error) {
      console.error(`处理 ${size}x${size} 图像时出错:`, error);
      // 如果Jimp处理失败，直接使用原始PNG缓冲区
      pngBuffers.push(pngBuffer);
    }
  }
  
  // 将PNG图像转换为ICO
  console.log('将PNG图像转换为ICO文件...');
  const icoBuffer = await toIco(pngBuffers);
  
  // 保存ICO文件
  fs.writeFileSync(OUTPUT_PATH, icoBuffer);
  console.log(`ICO文件已保存至: ${OUTPUT_PATH}`);
};

createImages().catch(err => {
  console.error('转换过程中出错:', err);
  process.exit(1);
}); 