{"compilerOptions": {"allowJs": true, "allowUnreachableCode": false, "baseUrl": "../../src", "esModuleInterop": true, "jsx": "react-jsx", "lib": ["DOM", "ESNext"], "module": "ESNext", "moduleResolution": "Node", "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUncheckedIndexedAccess": true, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "../../../dist", "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "strict": true, "strictBindCallApply": true, "strictNullChecks": true, "inlineSources": true, "target": "es5", "types": ["jest", "node", "react"]}, "exclude": ["../../node_modules"], "include": ["../../src/**/*", "../../__tests__/**/*", "../../DEV_ONLY/**/*"], "files": ["../../node_modules/jest-expect-message/types/index.d.ts"]}