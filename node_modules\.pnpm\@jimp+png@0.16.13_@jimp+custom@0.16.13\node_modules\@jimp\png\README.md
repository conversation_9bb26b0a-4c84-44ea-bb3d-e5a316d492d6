<div align="center">
  <img width="200" height="200"
    src="https://s3.amazonaws.com/pix.iemoji.com/images/emoji/apple/ios-11/256/crayon.png">
  <h1>@jimp/png</h1>
  <p>Default Jimp png encoder/decoder.</p>
</div>

## Available Methods

### Jimp.deflateLevel

Sets the deflate level used when saving as PNG format (default is 9)

### Jim<PERSON>.deflateStrategy

Sets the deflate strategy used when saving as PNG format (default is 3)

### Jimp.filterType

Sets the filter type used when saving as PNG format (default is automatic filters)

### Jimp.colorType

Sets the color type used when saving as PNG format (one of 0, 2, 4, 6)

### Filter Types

```js
Jimp.PNG_FILTER_AUTO;
Jimp.PNG_FILTER_NONE;
Jimp.PNG_FILTER_SUB;
Jimp.PNG_FILTER_UP;
Jimp.PNG_FILTER_AVERAGE;
Jimp.PNG_FILTER_PATH;
```
