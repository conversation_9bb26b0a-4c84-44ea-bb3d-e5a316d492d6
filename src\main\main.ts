import { app, BrowserWindow, ipcMain, Menu, session, shell } from 'electron';
import path from 'path';
import { fork, ChildProcess, spawn } from 'child_process';
import { DownloadManager } from './download-manager';
import fs from 'fs';
import puppeteer from 'puppeteer';

// 强制设置控制台输出编码为UTF-8
if (process.platform === 'win32') {
  try {
    // 设置控制台代码页为UTF-8
    const { execSync } = require('child_process');
    execSync('chcp 65001', { stdio: 'inherit' });
    console.log('[Main Process] 已设置控制台代码页为UTF-8');
    
    // 强制设置Node.js进程编码
    process.env.LANG = 'zh_CN.UTF-8';
    process.env.LC_ALL = 'zh_CN.UTF-8';
    process.env.LC_CTYPE = 'zh_CN.UTF-8';
    
    // 设置stdout和stderr的编码
    if (process.stdout.setEncoding) {
      process.stdout.setEncoding('utf8');
    }
    if (process.stderr.setEncoding) {
      process.stderr.setEncoding('utf8');
    }
  } catch (err) {
    console.error('[Main Process] 设置控制台代码页失败:', err);
  }
} else {
  // 非Windows平台也设置编码
  process.env.LANG = 'zh_CN.UTF-8';
  process.env.LC_ALL = 'zh_CN.UTF-8';
  process.env.LC_CTYPE = 'zh_CN.UTF-8';
}

let mainWindow: BrowserWindow | null;
let scraperProcess: ChildProcess | null = null;
let detailScraperProcess: ChildProcess | null = null;
let downloadManager: DownloadManager | null = null;
let cachedData: any[] | null = null; // 用于缓存抓取到的数据
let rendererReady = false; // 标记渲染进程是否已准备好
let isScraperRunning = false; // 标记爬虫是否正在运行
let searchScraperProcess: ChildProcess | null = null;
let translatorProcess: ChildProcess | null = null;

// 确保文本正确编码
function ensureUtf8(str: string): string {
  try {
    // 如果已经是有效的UTF-8字符串，直接返回
    if (Buffer.from(str).toString('utf8') === str) {
      return str;
    }
    // 尝试解码可能的非UTF-8编码
    const buffer = Buffer.from(str, 'binary');
    return buffer.toString('utf8');
  } catch (e) {
    console.error('[Main Process] 字符串编码转换失败:', e);
    return str; // 失败时返回原始字符串
  }
}

// 获取脚本路径的函数，处理开发环境和生产环境的不同路径
function getScriptPath(scriptName: string): string | null {
  let scriptPath: string;

  if (app.isPackaged) {
    console.log(`[Main Process] 打包环境，查找脚本: ${scriptName}`);
    console.log(`[Main Process] process.resourcesPath: ${process.resourcesPath}`);

    // 尝试多个可能的路径
    const possiblePaths = [
      path.join(process.resourcesPath, 'scraper', scriptName),
      path.join(process.resourcesPath, 'app.asar.unpacked', 'dist', 'scraper', scriptName),
      path.join(app.getAppPath(), 'resources', 'scraper', scriptName),
      path.join(__dirname, '..', 'scraper', scriptName)
    ];

    for (const testPath of possiblePaths) {
      console.log(`[Main Process] 检查脚本路径: ${testPath}`);
      if (fs.existsSync(testPath)) {
        console.log(`[Main Process] ✅ 找到脚本: ${testPath}`);
        return testPath;
      } else {
        console.log(`[Main Process] ❌ 脚本不存在: ${testPath}`);
      }
    }

    console.error(`[Main Process] 严重错误: 无法找到脚本 ${scriptName}`);

    // 列出资源目录内容以便调试
    try {
      const resourcesContent = fs.readdirSync(process.resourcesPath);
      console.log(`[Main Process] 资源目录内容: ${resourcesContent.join(', ')}`);

      const scraperPath = path.join(process.resourcesPath, 'scraper');
      if (fs.existsSync(scraperPath)) {
        const scraperContent = fs.readdirSync(scraperPath);
        console.log(`[Main Process] Scraper 目录内容: ${scraperContent.join(', ')}`);
      } else {
        console.log(`[Main Process] Scraper 目录不存在: ${scraperPath}`);
      }
    } catch (err) {
      console.error(`[Main Process] 无法读取目录: ${err}`);
    }

    return null;
  } else {
    // 开发环境
    scriptPath = path.join(app.getAppPath(), 'dist', 'scraper', scriptName);
    console.log(`[Main Process] 开发环境脚本路径: ${scriptPath}`);

    if (fs.existsSync(scriptPath)) {
      return scriptPath;
    } else {
      console.error(`[Main Process] 开发环境脚本不存在: ${scriptPath}`);
      return null;
    }
  }
}

// 动态获取Chromium可执行文件路径
function getChromiumExecutablePath(): string {
  if (app.isPackaged) {
    // 在打包应用中，Chromium 浏览器被打包到资源目录
    console.log(`[Main Process] 打包应用检测到`);
    console.log(`[Main Process] process.resourcesPath: ${process.resourcesPath}`);
    console.log(`[Main Process] app.getAppPath(): ${app.getAppPath()}`);
    console.log(`[Main Process] process.execPath: ${process.execPath}`);
    console.log(`[Main Process] __dirname: ${__dirname}`);

    // 尝试多个可能的路径
    const possiblePaths = [
      path.join(process.resourcesPath, 'chromium', 'chrome.exe'),
      path.join(app.getAppPath(), 'resources', 'chromium', 'chrome.exe'),
      path.join(path.dirname(process.execPath), 'resources', 'chromium', 'chrome.exe'),
      path.join(__dirname, '..', '..', 'chromium', 'chrome.exe'),
      path.join(__dirname, '..', '..', 'resources', 'chromium', 'chrome.exe')
    ];

    for (const executablePath of possiblePaths) {
      console.log(`[Main Process] 检查 Chromium 路径: ${executablePath}`);
      if (fs.existsSync(executablePath)) {
        console.log(`[Main Process] ✅ 找到 Chromium: ${executablePath}`);
        return executablePath;
      } else {
        console.log(`[Main Process] ❌ 路径不存在: ${executablePath}`);
      }
    }

    // 如果所有路径都不存在，记录详细信息并抛出错误
    console.error(`[Main Process] 严重错误: 无法找到打包的 Chromium 浏览器`);
    console.error(`[Main Process] 已检查的路径:`);
    possiblePaths.forEach(p => console.error(`  - ${p}`));

    // 列出资源目录的内容以便调试
    try {
      const resourcesContent = fs.readdirSync(process.resourcesPath);
      console.log(`[Main Process] 资源目录内容: ${resourcesContent.join(', ')}`);

      const chromiumPath = path.join(process.resourcesPath, 'chromium');
      if (fs.existsSync(chromiumPath)) {
        const chromiumContent = fs.readdirSync(chromiumPath);
        console.log(`[Main Process] Chromium 目录内容: ${chromiumContent.join(', ')}`);
      }
    } catch (err) {
      console.error(`[Main Process] 无法读取资源目录: ${err}`);
    }

    // 返回第一个路径作为默认值，即使它不存在
    return possiblePaths[0];
  } else {
    // 在开发模式中，Puppeteer 自动查找可执行文件
    const devPath = puppeteer.executablePath();
    console.log(`[Main Process] 开发模式。使用 Puppeteer 默认路径: ${devPath}`);
    return devPath;
  }
}

// 启动抓取器进程，使用更节省资源的方式
function startScraperProcess(page: number = 1) {
  // 如果爬虫正在运行，不再重复启动
  if (isScraperRunning) {
    console.log('[Main Process] 爬虫进程正在运行，忽略此次请求');
    return;
  }

  // 如果已存在进程，先确保关闭
  if (scraperProcess) {
    console.log('[Main Process] 抓取进程已存在，先关闭...');
    try {
      scraperProcess.kill();
    } catch (err) {
      console.error('[Main Process] 关闭爬虫进程时出错:', err);
    }
    scraperProcess = null;
  }

  isScraperRunning = true; // 标记爬虫开始运行
  
  const scraperScriptPath = getScriptPath('scraper.js');
  
  if (!scraperScriptPath) {
    // 通知渲染进程出错了
    if (mainWindow) {
      mainWindow.webContents.send('update-trainer-data-error', '找不到爬虫脚本文件，请重新构建应用');
    }
    
    isScraperRunning = false;
    return;
  }
  
  console.log(`[Main Process] 启动脚本: ${scraperScriptPath}`);
  
  const chromiumPath = getChromiumExecutablePath();
  console.log(`[Main Process] 使用的Chromium路径: ${chromiumPath}`);
  
  // 验证 Chromium 路径是否存在
  if (!fs.existsSync(chromiumPath)) {
    console.error(`[Main Process] Chromium 可执行文件不存在: ${chromiumPath}`);
    if (mainWindow) {
      mainWindow.webContents.send('update-trainer-data-error', `Chromium 浏览器未找到: ${chromiumPath}`);
    }
    isScraperRunning = false;
    return;
  }

  try {
    // 使用 fork 方法启动子进程，并增加内存限制，传递页码和浏览器路径参数
    scraperProcess = fork(scraperScriptPath, [page.toString(), chromiumPath], {
      stdio: ['pipe', 'pipe', 'pipe', 'ipc'],
      // 设置更高的内存限制
      execArgv: ['--max-old-space-size=1024'],
      // 确保使用正确的编码
      env: {
        ...process.env,
        LANG: 'zh_CN.UTF-8',
        LC_ALL: 'zh_CN.UTF-8',
        LC_CTYPE: 'zh_CN.UTF-8',
        NODE_OPTIONS: '--no-warnings --max-old-space-size=1024',
        ELECTRON_RUN_AS_NODE: '1',
        // 注入正确的Chromium路径，Puppeteer会自动使用这个环境变量
        PUPPETEER_EXECUTABLE_PATH: chromiumPath,
      }
    });

    console.log('[Main Process] ✅ 已创建爬虫子进程，设置了IPC通道');
    console.log(`[Main Process] 子进程 PID: ${scraperProcess.pid}`);
  } catch (error) {
    console.error('[Main Process] ❌ 创建子进程失败:', error);
    if (mainWindow) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      mainWindow.webContents.send('update-trainer-data-error', `启动爬虫失败: ${errorMessage}`);
    }
    isScraperRunning = false;
    return;
  }

  // 监听子进程错误
  scraperProcess.on('error', (error: Error) => {
    console.error('[Main Process] 子进程错误:', error);
    if (mainWindow) {
      mainWindow.webContents.send('update-trainer-data-error', `爬虫进程错误: ${error.message}`);
    }
    isScraperRunning = false;
    scraperProcess = null;
  });

  // 监听子进程退出
  scraperProcess.on('exit', (code, signal) => {
    console.log(`[Main Process] 子进程退出，代码: ${code}, 信号: ${signal}`);
    if (code !== 0 && code !== null) {
      console.error(`[Main Process] 子进程异常退出，代码: ${code}`);
      if (mainWindow) {
        mainWindow.webContents.send('update-trainer-data-error', `爬虫进程异常退出，代码: ${code}`);
      }
    }
    isScraperRunning = false;
    scraperProcess = null;
  });

  scraperProcess.stdout?.on('data', (data) => {
    try {
      const output = data.toString('utf8');
      console.log(`[Scraper stdout] ${output}`);
    } catch (err) {
      console.error('[Main Process] 处理爬虫输出时出错:', err);
    }
  });
  
  scraperProcess.stderr?.on('data', (data) => {
    try {
      const output = data.toString('utf8');
      console.error(`[Scraper stderr] ${output}`);
    } catch (err) {
      console.error('[Main Process] 处理爬虫错误输出时出错:', err);
    }
  });

  // 设置超时，确保爬虫不会无限运行
  const scraperTimeout = setTimeout(() => {
    if (scraperProcess) {
      console.log('[Main Process] 爬虫运行超时，强制终止');
      try {
        scraperProcess.kill();
      } catch (err) {
        console.error('[Main Process] 终止爬虫进程时出错:', err);
      }
      scraperProcess = null;
      isScraperRunning = false;
      
      // 通知渲染进程爬虫超时
      if (mainWindow) {
        mainWindow.webContents.send('update-trainer-data-error', '爬虫运行超时，请稍后重试');
      }
    }
  }, 45000); // 45秒超时

  // 监听来自爬虫进程的消息
  scraperProcess.on('message', (message: { success: boolean; data?: any; error?: string; pagination?: any }) => {
    // 收到消息，清除超时计时器
    clearTimeout(scraperTimeout);
    
    console.log('[Main Process] 收到爬虫数据:', JSON.stringify(message).substring(0, 100) + '...');
    if (message.success) {
      console.log(`[Main Process] 数据项数量: ${message.data.length}`);
      
      // 先缓存数据
      cachedData = message.data;
      
      // 获取分页信息
      const paginationInfo = message.pagination || {
        currentPage: page,
        hasNextPage: true, // 默认有下一页
        hasPrevPage: page > 1, // 第1页没有上一页
        totalPages: 10 // 假设有10页，实际情况可能需要动态计算
      };

      // 确保分页信息完整
      console.log('[Main Process] 分页信息:', JSON.stringify(paginationInfo));
      
      // 如果渲染进程已经准备好了，立即发送数据
      if (rendererReady && mainWindow) {
        mainWindow.webContents.send('update-trainer-data', cachedData, paginationInfo);
        console.log('[Main Process] 数据已发送到渲染进程');
      } else {
        console.log('[Main Process] 数据已缓存，等待渲染进程就绪...');
      }
    } else {
      console.error('[Main Process] 爬虫脚本执行失败:', message.error);
      
      // 通知渲染进程出错了
      if (mainWindow) {
        mainWindow.webContents.send('update-trainer-data-error', message.error || '未知错误');
      }
    }
    
    // 标记爬虫已完成运行
    isScraperRunning = false;
  });

  scraperProcess.on('exit', (code) => {
    console.log(`[Main Process] 爬虫进程退出，退出码: ${code}`);
    scraperProcess = null;
    isScraperRunning = false;
    clearTimeout(scraperTimeout); // 确保清除超时计时器
  });
  
  // 监听错误事件
  scraperProcess.on('error', (err) => {
    console.error('[Main Process] 爬虫进程出错:', err);
    scraperProcess = null;
    isScraperRunning = false;
    clearTimeout(scraperTimeout);
  });
}

// 启动详情页抓取进程
function startDetailScraperProcess(trainerLink: string) {
  // 如果已存在进程，先确保完全关闭
  if (detailScraperProcess) {
    console.log('[Main Process] 详情抓取进程已存在，先关闭...');
    try {
      // 移除所有事件监听器，防止内存泄漏
      detailScraperProcess.removeAllListeners();
      detailScraperProcess.kill('SIGKILL'); // 使用SIGKILL强制终止进程
      
      // 等待一小段时间确保进程完全关闭
      setTimeout(() => {
        console.log('[Main Process] 旧的详情抓取进程已强制关闭，现在启动新进程');
        initDetailScraperProcess(trainerLink);
      }, 100);
    } catch (err) {
      console.error('[Main Process] 关闭详情抓取进程时出错:', err);
      detailScraperProcess = null;
      // 直接启动新进程
      initDetailScraperProcess(trainerLink);
    }
  } else {
    // 直接启动新进程
    initDetailScraperProcess(trainerLink);
  }
}

// 实际初始化详情抓取进程的函数
function initDetailScraperProcess(trainerLink: string) {
  const detailScraperScriptPath = getScriptPath('detail-scraper.js');
  
  if (!detailScraperScriptPath) {
    // 通知渲染进程出错了
    if (mainWindow) {
      mainWindow.webContents.send('trainer-download-options-response', {
        success: false,
        trainerLink,
        error: '找不到详情爬虫脚本文件，请重新构建应用'
      });
    }
    
    return;
  }
  
  console.log(`[Main Process] 启动详情抓取脚本: ${detailScraperScriptPath}`);
  
  // 使用 fork 方法启动子进程，并增加内存限制
  detailScraperProcess = fork(detailScraperScriptPath, [trainerLink], { 
    stdio: ['pipe', 'pipe', 'pipe', 'ipc'],
    // 设置更高的内存限制
    execArgv: ['--max-old-space-size=1024'],
    // 确保使用正确的编码
    env: { 
      ...process.env, 
      LANG: 'zh_CN.UTF-8', 
      LC_ALL: 'zh_CN.UTF-8', 
      LC_CTYPE: 'zh_CN.UTF-8',
      NODE_OPTIONS: '--no-warnings --max-old-space-size=1024',
      ELECTRON_RUN_AS_NODE: '1'
    }
  });

  console.log('[Main Process] 已创建详情抓取子进程');

  detailScraperProcess.stdout?.on('data', (data) => {
    try {
      const output = data.toString('utf8');
      console.log(`[Detail Scraper stdout] ${output}`);
    } catch (err) {
      console.error('[Main Process] 处理详情爬虫输出时出错:', err);
    }
  });
  
  detailScraperProcess.stderr?.on('data', (data) => {
    try {
      const output = data.toString('utf8');
      console.error(`[Detail Scraper stderr] ${output}`);
    } catch (err) {
      console.error('[Main Process] 处理详情爬虫错误输出时出错:', err);
    }
  });

  // 设置超时，确保详情爬虫不会无限运行
  const detailScraperTimeout = setTimeout(() => {
    if (detailScraperProcess) {
      console.log('[Main Process] 详情爬虫运行超时，强制终止');
      try {
        detailScraperProcess.removeAllListeners(); // 移除所有监听器
        detailScraperProcess.kill('SIGKILL');
      } catch (err) {
        console.error('[Main Process] 终止详情爬虫进程时出错:', err);
      }
      detailScraperProcess = null;
      
      // 通知渲染进程详情爬虫超时
      if (mainWindow) {
        mainWindow.webContents.send('trainer-download-options-response', {
          success: false,
          trainerLink,
          error: '详情爬虫运行超时，请稍后重试'
        });
      }
    }
  }, 30000); // 30秒超时

  // 监听来自详情爬虫进程的消息
  detailScraperProcess.on('message', (message: { 
    success: boolean; 
    trainerLink: string;
    downloadOptions?: any[];
    error?: string 
  }) => {
    // 收到消息，清除超时计时器
    clearTimeout(detailScraperTimeout);
    
    console.log('[Main Process] 收到详情数据:', JSON.stringify(message).substring(0, 100) + '...');
    
    // 将数据发送到渲染进程
    if (mainWindow) {
      mainWindow.webContents.send('trainer-download-options-response', message);
      console.log('[Main Process] 详情数据已发送到渲染进程');
    }
    
    // 数据发送完毕后关闭进程
    try {
      if (detailScraperProcess) {
        detailScraperProcess.removeAllListeners();
        detailScraperProcess.kill();
        detailScraperProcess = null;
      }
    } catch (err) {
      console.error('[Main Process] 关闭详情抓取进程时出错:', err);
    }
  });

  detailScraperProcess.on('exit', (code) => {
    console.log(`[Main Process] 详情抓取进程退出，退出码: ${code}`);
    detailScraperProcess = null;
    clearTimeout(detailScraperTimeout); // 确保清除超时计时器
  });
  
  // 监听错误事件
  detailScraperProcess.on('error', (err) => {
    console.error('[Main Process] 详情抓取进程出错:', err);
    detailScraperProcess = null;
    clearTimeout(detailScraperTimeout);
    
    // 通知渲染进程错误
    if (mainWindow) {
      mainWindow.webContents.send('trainer-download-options-response', {
        success: false,
        trainerLink,
        error: '详情抓取进程出错: ' + err.message
      });
    }
  });
}

// 启动搜索爬虫进程
function startSearchScraperProcess(searchQuery: string) {
  console.log(`[Main Process] 开始搜索: "${searchQuery}"`);
  
  // 如果已存在进程，先确保关闭
  if (searchScraperProcess) {
    console.log('[Main Process] 搜索抓取进程已存在，先关闭...');
    try {
      searchScraperProcess.kill();
    } catch (err) {
      console.error('[Main Process] 关闭搜索抓取进程时出错:', err);
    }
    searchScraperProcess = null;
  }

  const searchScraperScriptPath = getScriptPath('search-scraper.js');
  
  if (!searchScraperScriptPath) {
    console.error('[Main Process] 错误: 找不到搜索爬虫脚本文件!');
    
    // 通知渲染进程出错了
    if (mainWindow) {
      mainWindow.webContents.send('search-results-response', {
        success: false,
        query: searchQuery,
        error: '找不到搜索爬虫脚本文件，请重新构建应用'
      });
    }
    
    return;
  }
  
  console.log(`[Main Process] 启动搜索爬虫脚本: ${searchScraperScriptPath}`);
  
  // 使用 fork 方法启动子进程
  searchScraperProcess = fork(searchScraperScriptPath, [searchQuery], { 
    stdio: ['pipe', 'pipe', 'pipe', 'ipc'],
    execArgv: ['--max-old-space-size=1024'],
    env: { 
      ...process.env, 
      LANG: 'zh_CN.UTF-8', 
      LC_ALL: 'zh_CN.UTF-8', 
      LC_CTYPE: 'zh_CN.UTF-8',
      NODE_OPTIONS: '--no-warnings --max-old-space-size=1024',
      ELECTRON_RUN_AS_NODE: '1'
    }
  });

  console.log('[Main Process] 已创建搜索爬虫子进程');

  searchScraperProcess.stdout?.on('data', (data) => {
    try {
      const output = data.toString('utf8');
      console.log(`[Search Scraper stdout] ${output}`);
    } catch (err) {
      console.error('[Main Process] 处理爬虫输出时出错:', err);
    }
  });
  
  searchScraperProcess.stderr?.on('data', (data) => {
    try {
      const output = data.toString('utf8');
      console.error(`[Search Scraper stderr] ${output}`);
    } catch (err) {
      console.error('[Main Process] 处理爬虫错误输出时出错:', err);
    }
  });

  // 设置超时
  const searchScraperTimeout = setTimeout(() => {
    if (searchScraperProcess) {
      console.log('[Main Process] 搜索爬虫运行超时，强制终止');
      try {
        searchScraperProcess.kill();
      } catch (err) {
        console.error('[Main Process] 终止搜索爬虫进程时出错:', err);
      }
      
      // 通知渲染进程搜索超时
      if (mainWindow) {
        mainWindow.webContents.send('search-results-response', {
          success: false,
          query: searchQuery,
          error: '搜索超时，请稍后重试'
        });
      }
    }
  }, 30000); // 30秒超时

  // 监听来自搜索爬虫进程的消息
  searchScraperProcess.on('message', (message: { 
    success: boolean; 
    results?: any[];
    data?: any[];
    error?: string;
    query?: string;
  }) => {
    // 收到消息，清除超时计时器
    clearTimeout(searchScraperTimeout);
    
    console.log('[Main Process] 收到搜索结果:', JSON.stringify(message).substring(0, 100) + '...');
    
    // 将数据发送到渲染进程
    if (mainWindow) {
      // 确保结果字段名一致
      const results = message.results || message.data || [];
      const response = {
        success: message.success,
        results: results,
        query: message.query || searchQuery,
        error: message.error
      };
      
      mainWindow.webContents.send('search-results-response', response);
      console.log(`[Main Process] 搜索结果已发送到渲染进程: ${results.length} 条结果`);
    }
  });

  searchScraperProcess.on('exit', (code) => {
    console.log(`[Main Process] 搜索爬虫进程退出，退出码: ${code}`);
    clearTimeout(searchScraperTimeout); // 确保清除超时计时器
  });
  
  // 监听错误事件
  searchScraperProcess.on('error', (err) => {
    console.error('[Main Process] 搜索爬虫进程出错:', err);
    clearTimeout(searchScraperTimeout);
    
    // 通知渲染进程出错
    if (mainWindow) {
      mainWindow.webContents.send('search-results-response', {
        success: false,
        query: searchQuery,
        error: `搜索爬虫进程出错: ${err.message}`
      });
    }
  });
}

// 总是从文件加载，因为我们不再使用 dev server
function loadMainWindow() {
  if (!mainWindow || mainWindow.isDestroyed()) return;
  
  const indexPath = path.join(__dirname, '../renderer/index.html');
  console.log(`[Main Process] 加载渲染进程文件: ${indexPath}`);
  
  // 验证文件是否存在
  if (!fs.existsSync(indexPath)) {
    console.error(`[Main Process] 错误: 渲染文件不存在: ${indexPath}`);
    
    // 尝试查找可能的位置
    const possibleLocations = [
      path.join(app.getAppPath(), 'dist/renderer/index.html'),
      path.join(process.cwd(), 'dist/renderer/index.html')
    ];
    
    for (const loc of possibleLocations) {
      console.log(`[Main Process] 尝试备选位置: ${loc}`);
      if (fs.existsSync(loc)) {
        console.log(`[Main Process] 找到备选文件位置: ${loc}`);
        mainWindow.loadFile(loc).catch(err => {
          console.error('[Main Process] 加载渲染进程文件失败:', err);
        });
        return;
      }
    }
    
    // 如果还是找不到，显示错误信息
    mainWindow.loadURL(`data:text/html,<html><body><h1>错误：找不到界面文件</h1><p>请重新构建应用</p></body></html>`);
    return;
  }
  
  mainWindow.loadFile(indexPath).catch(err => {
    console.error('[Main Process] 加载渲染进程文件失败:', err);
  });
}

function createWindow() {
  // 检查是否已存在窗口，避免重复创建
  if (mainWindow && !mainWindow.isDestroyed()) {
    console.log('[Main Process] 窗口已存在，跳过创建');
    mainWindow.focus();
    return;
  }

  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    frame: false,
    titleBarStyle: 'hidden', // 隐藏默认的标题栏
    // 完全禁用系统搜索框
    titleBarOverlay: false,
    fullscreenable: true,
    // 设置窗口性能参数
    show: false,
    backgroundColor: '#1a1a1a', // 深色背景，减少闪烁
    paintWhenInitiallyHidden: true,
    enableLargerThanScreen: false,
    // 窗口设置
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      // 安全配置
      nodeIntegration: false,
      contextIsolation: true,
      sandbox: true,
      // 性能优化
      backgroundThrottling: false,
      // 减少不必要的功能以提高性能
      spellcheck: false,
      webgl: true,
      plugins: false,
      experimentalFeatures: false,
      autoplayPolicy: 'no-user-gesture-required' as const
    }
  });

  // 进一步优化窗口性能
  if (mainWindow.webContents) {
    mainWindow.webContents.backgroundThrottling = false;
    
    // 设置内容预热，提高首次渲染速度
    mainWindow.webContents.once('did-finish-load', () => {
      console.log('[Main Process] 窗口内容加载完成');
      
      // 预先进行一次渲染，避免首次渲染卡顿
      mainWindow?.webContents.setVisualZoomLevelLimits(1, 1);
    });
    
    // 优化窗口显示逻辑
    mainWindow.once('ready-to-show', () => {
      console.log('[Main Process] 窗口准备好显示');
      mainWindow?.show();
      mainWindow?.focus();
    });
    
    // 监听渲染进程崩溃事件，使用electron标准事件
    mainWindow.webContents.on('render-process-gone', (event, details) => {
      console.error(`[Main Process] 渲染进程异常: ${details.reason}`);
      
      // 尝试重载窗口
      if (mainWindow && !mainWindow.isDestroyed()) {
        console.log('[Main Process] 尝试重新加载窗口...');
        mainWindow.reload();
      }
    });
  }

  // 初始化下载管理器
  downloadManager = new DownloadManager(mainWindow);

  // 监听来自渲染进程的窗口控制事件
  ipcMain.on('window-minimize', () => {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.minimize();
    }
  });
  
  ipcMain.on('window-maximize', () => {
    if (!mainWindow || mainWindow.isDestroyed()) return;
    
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  });
  
  ipcMain.on('window-close', () => {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.close();
    }
  });

  // 监听渲染进程的就绪信号
  ipcMain.on('renderer-ready', () => {
    console.log('[Main Process] 收到渲染进程就绪信号，时间:', new Date().toLocaleTimeString());
    rendererReady = true;
    
    // 如果已有缓存数据，立即发送
    if (cachedData && mainWindow && !mainWindow.isDestroyed()) {
      console.log(`[Main Process] 发送缓存数据 (${cachedData.length}项)...`);
      try {
        mainWindow.webContents.send('update-trainer-data', cachedData);
      } catch (err) {
        console.error('[Main Process] 发送缓存数据时出错:', err);
      }
    } else {
      console.log('[Main Process] 没有缓存数据可发送，启动爬虫获取数据');
      // 如果没有缓存数据且爬虫未运行，自动获取数据
      if (!isScraperRunning && !cachedData) {
        startScraperProcess();
      }
    }
  });

  // 添加搜索游戏的IPC处理器
  ipcMain.on('search-trainers', (event, searchQuery) => {
    console.log(`[Main Process] 收到搜索请求: "${searchQuery}"`);
    
    // 确保搜索关键词是字符串并且非空
    if (!searchQuery || typeof searchQuery !== 'string' || searchQuery.trim() === '') {
      console.error('[Main Process] 搜索关键词无效:', searchQuery);
      if (mainWindow) {
        mainWindow.webContents.send('search-results-response', {
          success: false,
          results: [],
          error: '搜索关键词无效'
        });
      }
      return;
    }
    
    // 确保中文编码正确
    try {
      // 记录原始搜索关键词和编码后的关键词
      console.log(`[Main Process] 原始搜索关键词: "${searchQuery}"`);
      console.log(`[Main Process] 编码: ${Buffer.from(searchQuery).toString('hex')}`);
      
      // 启动搜索爬虫进程
      startSearchScraperProcess(searchQuery);
    } catch (err) {
      const error = err as Error;
      console.error('[Main Process] 处理搜索请求时出错:', error);
      if (mainWindow) {
        mainWindow.webContents.send('search-results-response', {
          success: false,
          results: [],
          error: `处理搜索请求时出错: ${error.message}`
        });
      }
    }
  });

  // 监听渲染进程请求数据刷新
  ipcMain.on('request-trainer-data', (event, page = 1) => {
    console.log(`[Main Process] 收到数据刷新请求，页码: ${page}`);
    
    // 如果没有缓存数据或强制刷新，则启动爬虫
    if (!cachedData || page > 1) {
      console.log('[Main Process] 没有缓存数据或请求非首页，启动爬虫获取数据');
      startScraperProcess(page);
    } else {
      console.log('[Main Process] 使用缓存数据');
      // 使用缓存数据
      if (mainWindow) {
        // 创建分页信息
        const paginationInfo = {
          currentPage: 1,
          hasNextPage: true, // 确保首页总是有下一页
          hasPrevPage: false, // 第1页没有上一页
          totalPages: 10 // 假设有10页，实际情况可能需要动态计算
        };
        
        mainWindow.webContents.send('update-trainer-data', cachedData, paginationInfo);
        console.log('[Main Process] 缓存数据已发送到渲染进程');
      }
    }
  });

  // 监听获取特定游戏下载选项的请求
  ipcMain.on('request-trainer-download-options', (event, trainerLink) => {
    console.log(`[Main Process] 收到获取下载选项请求: ${trainerLink}`);
    startDetailScraperProcess(trainerLink);
  });

  // 监听下载文件请求
  ipcMain.on('download-file', (event, url, filename) => {
    // 确保文件名正确编码
    const sanitizedFilename = ensureUtf8(filename);
    console.log(`[Main Process] 收到下载文件请求: ${url}, 文件名: ${sanitizedFilename}`);
    if (downloadManager) {
      downloadManager.download(url, sanitizedFilename);
    }
  });

  // 监听获取下载列表请求
  ipcMain.on('get-downloads', (event) => {
    if (downloadManager) {
      const downloads = downloadManager.getAllDownloads();
      // 不需要在这里转换，已在download-manager中处理
      event.reply('downloads-list-update', downloads);
    }
  });

  // 监听清除已完成下载请求
  ipcMain.on('clear-completed-downloads', () => {
    if (downloadManager) {
      downloadManager.clearCompletedDownloads();
    }
  });

  // 监听删除单个下载请求
  ipcMain.on('delete-download', (event, downloadId) => {
    console.log(`[Main Process] 收到删除下载请求: ${downloadId}`);
    if (downloadManager) {
      const result = downloadManager.deleteDownload(downloadId);
      console.log(`[Main Process] 删除下载${result ? '成功' : '失败'}: ${downloadId}`);
    }
  });

  // 监听设置下载目录请求
  ipcMain.on('set-download-folder', (event, folder) => {
    if (downloadManager) {
      const sanitizedFolder = ensureUtf8(folder);
      downloadManager.setDownloadFolder(sanitizedFolder);
      event.reply('download-folder-updated', sanitizedFolder);
    }
  });

  // 监听获取下载目录请求
  ipcMain.on('get-download-folder', (event) => {
    if (downloadManager) {
      const folder = downloadManager.getDownloadFolder();
      const sanitizedFolder = ensureUtf8(folder);
      event.reply('download-folder', sanitizedFolder);
    }
  });

  // 添加监听打开外部链接的请求
  ipcMain.on('open-external-url', (event, url) => {
    console.log(`[Main Process] 收到打开外部链接请求: "${url}"`);
    
    // 白名单URL检查
    const validUrls = [
      'https://flingtrainer.com/',
      'https://flingtrainer.com',
      'https://www.baidu.com/' // 添加百度域名到白名单
    ];
    
    // 检查URL是否以白名单中的任一URL开头
    const isValidUrl = validUrls.some(validUrl => 
      url === validUrl || url.startsWith(`${validUrl}/`)
    );
    
    if (isValidUrl) {
      console.log(`[Main Process] URL在白名单中，准备打开: "${url}"`);
      
      shell.openExternal(url)
        .then(() => {
          console.log(`[Main Process] 成功打开链接: "${url}"`);
        })
        .catch((err) => {
          console.error(`[Main Process] 打开链接失败: ${err}`);
          
          // 如果常规方法失败，尝试其他方式打开
          try {
            const { exec } = require('child_process');
            if (process.platform === 'win32') {
              console.log('[Main Process] 尝试使用windows start命令打开URL');
              exec(`start ${url}`);
            } else if (process.platform === 'darwin') {
              console.log('[Main Process] 尝试使用macOS open命令打开URL');
              exec(`open ${url}`);
            } else {
              console.log('[Main Process] 尝试使用xdg-open命令打开URL');
              exec(`xdg-open ${url}`);
            }
          } catch (execErr) {
            console.error(`[Main Process] 尝试使用exec打开URL失败: ${execErr}`);
          }
        });
    } else {
      console.error(`[Main Process] 拒绝打开非白名单URL: "${url}"`);
    }
  });

  // 处理游戏名称翻译请求
  ipcMain.handle('translate-game-name', async (event, gameName: string) => {
    console.log('收到游戏名称翻译请求:', gameName);
    
    try {
      // 如果已有翻译进程在运行，先终止它
      if (translatorProcess) {
        console.log('终止之前的翻译进程');
        translatorProcess.kill();
        translatorProcess = null;
      }
      
      // 启动翻译进程
      console.log('启动翻译进程...');
      const translatorScriptPath = getScriptPath('translator.js');
      console.log('翻译脚本路径:', translatorScriptPath);

      if (!translatorScriptPath) {
        console.error('翻译脚本路径无效');
        (event as any).reply('search-trainers-reply', { success: false, error: '翻译脚本未找到' });
        return;
      }

      translatorProcess = fork(translatorScriptPath, [], {
        stdio: ['pipe', 'pipe', 'pipe', 'ipc']
      });
      
      // 设置超时
      const timeoutId = setTimeout(() => {
        if (translatorProcess) {
          console.log('翻译请求超时，终止进程');
          translatorProcess.kill();
          translatorProcess = null;
        }
      }, 30000); // 30秒超时
      
      return new Promise((resolve, reject) => {
        if (!translatorProcess) {
          clearTimeout(timeoutId);
          return reject(new Error('无法启动翻译进程'));
        }
        
        // 监听翻译结果
        translatorProcess.on('message', (data) => {
          console.log('收到翻译结果:', data);
          clearTimeout(timeoutId);
          
          // 完成后关闭进程
          if (translatorProcess) {
            translatorProcess.kill();
            translatorProcess = null;
          }
          
          resolve(data);
        });
        
        // 监听错误
        translatorProcess.on('error', (err) => {
          console.error('翻译进程错误:', err);
          clearTimeout(timeoutId);
          
          if (translatorProcess) {
            translatorProcess.kill();
            translatorProcess = null;
          }
          
          reject(err);
        });
        
        // 发送游戏名称到翻译进程
        translatorProcess.send({ gameName });
      });
    } catch (error: any) {
      console.error('处理翻译请求时出错:', error);
      return { success: false, error: error.message || '翻译过程中出错' };
    }
  });

  // 监听获取下载选项请求
  ipcMain.handle('get-download-options', async (event, trainerLink) => {
    console.log(`[Main Process] 收到获取下载选项请求: ${trainerLink}`);
    
    return new Promise((resolve) => {
      // 创建一次性函数来处理详情爬虫的结果
      function handleDetailScraperResult(result: any) {
        if (result.trainerLink === trainerLink) {
          // 返回结果
          resolve(result);
        }
      }
      
      // 替换原有的消息处理函数
      if (detailScraperProcess) {
        detailScraperProcess.removeAllListeners('message');
        detailScraperProcess.on('message', handleDetailScraperResult);
      }
      
      // 启动详情爬虫进程
      startDetailScraperProcess(trainerLink);
      
      // 设置超时，避免永久等待
      setTimeout(() => {
        resolve({
          success: false,
          trainerLink,
          error: '获取下载选项超时'
        });
      }, 30000); // 30秒超时
    });
  });

  // 调用新的加载函数
  loadMainWindow();
  
  // 在开发模式下自动打开开发者工具
  if (process.env.NODE_ENV === 'development') {
    // mainWindow.webContents.openDevTools();
  }
  
  // 监听窗口关闭事件，清理资源
  mainWindow.on('closed', () => {
    console.log('[Main Process] 主窗口已关闭');
    mainWindow = null;
    
    // 关闭所有子进程
    if (scraperProcess) {
      try {
        scraperProcess.kill();
      } catch (err) {
        console.error('[Main Process] 关闭爬虫进程时出错:', err);
      }
      scraperProcess = null;
    }
    
    if (detailScraperProcess) {
      try {
        detailScraperProcess.kill();
      } catch (err) {
        console.error('[Main Process] 关闭详情爬虫进程时出错:', err);
      }
      detailScraperProcess = null;
    }
  });
}

// 确保只有一个应用实例
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  console.log('[Main Process] 已有实例正在运行，退出此实例');
  app.quit();
} else {
  app.on('second-instance', () => {
    // 有人试图启动第二个实例，我们应该聚焦到我们的窗口
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });

  app.whenReady().then(() => {
    console.log('[Main Process] 应用已就绪，创建窗口');
    createWindow();

    // 当应用程序激活且没有窗口时创建窗口
    app.on('activate', function () {
      if (BrowserWindow.getAllWindows().length === 0) createWindow();
    });
  });
}

// 当所有窗口关闭时退出应用
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// 应用程序即将退出
app.on('before-quit', () => {
  console.log('[Main Process] 应用程序即将退出，清理资源');
  
  // 确保关闭所有子进程
  if (scraperProcess) {
    try {
      scraperProcess.kill();
    } catch (err) {
      console.error('[Main Process] 退出前关闭爬虫进程时出错:', err);
    }
  }
  
  if (detailScraperProcess) {
    try {
      detailScraperProcess.kill();
    } catch (err) {
      console.error('[Main Process] 退出前关闭详情爬虫进程时出错:', err);
    }
  }
});

// 应用退出时清理资源
app.on('will-quit', () => {
  // 终止所有子进程
  if (scraperProcess) {
    scraperProcess.kill();
    scraperProcess = null;
  }
  
  if (detailScraperProcess) {
    detailScraperProcess.kill();
    detailScraperProcess = null;
  }
  
  if (searchScraperProcess) {
    searchScraperProcess.kill();
    searchScraperProcess = null;
  }
  
  if (translatorProcess) {
    translatorProcess.kill();
    translatorProcess = null;
  }
});

// 启动翻译进程
async function startTranslationProcess(gameName: string): Promise<string | null> {
  const translatorScriptPath = getScriptPath('translator.js');
  
  if (!translatorScriptPath) {
    console.error('[Main Process] 错误: 找不到翻译脚本文件!');
    
    // 尝试使用内置方法直接进行翻译
    try {
      const fetch = require('node-fetch');
      const { JSDOM } = require('jsdom');
      
      console.log(`[Main Process] 使用内置方法翻译游戏名称: "${gameName}"`);
      
      // 构建百度搜索URL
      const searchQuery = `${gameName}的英文游戏名称`;
      const encodedQuery = encodeURIComponent(searchQuery);
      const searchUrl = `https://www.baidu.com/s?wd=${encodedQuery}`;
      
      console.log(`[Main Process] 请求URL: ${searchUrl}`);
      
      // 发起请求
      const response = await fetch(searchUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });
      
      if (!response.ok) {
        throw new Error(`请求失败: ${response.status} ${response.statusText}`);
      }
      
      const html = await response.text();
      console.log(`[Main Process] 获取到HTML响应，长度: ${html.length}`);
      
      // 使用JSDOM解析HTML
      const dom = new JSDOM(html);
      const document = dom.window.document;
      
      // 尝试提取AI结果中的游戏名称
      const markers = document.querySelectorAll('mark.flexible-marker');
      let englishName = null;
      
      for (const marker of markers) {
        const text = marker.textContent || '';
        // 检查是否包含英文游戏名称特征
        if (/[A-Za-z]+\s*[:\-]?\s*[A-Za-z]+/.test(text) && !text.includes('《') && text.length > 3) {
          englishName = text.trim();
          console.log(`[Main Process] 找到可能的英文名称: "${englishName}"`);
          break;
        }
      }
      
      // 如果上面的方法找不到，尝试其他选择器
      if (!englishName) {
        const aiContent = document.querySelector('.cosd-markdown-content');
        if (aiContent) {
          const strongElements = aiContent.querySelectorAll('strong');
          for (const strong of strongElements) {
            const text = strong.textContent || '';
            if (/[A-Za-z]+\s*[:\-]?\s*[A-Za-z]+/.test(text) && !text.includes('《')) {
              englishName = text.trim();
              console.log(`[Main Process] 从strong标签找到英文名称: "${englishName}"`);
              break;
            }
          }
        }
      }
      
      // 清理结果，移除引号等
      if (englishName) {
        englishName = englishName.replace(/[""]/g, '').trim();
        console.log(`[Main Process] 最终英文名称: "${englishName}"`);
      } else {
        console.log(`[Main Process] 未找到英文名称`);
      }
      
      return englishName;
    } catch (error) {
      console.error('[Main Process] 内置翻译方法出错:', error);
      return null;
    }
  }
  
  console.log(`[Main Process] 启动翻译脚本: ${translatorScriptPath}`);
  
  return new Promise((resolve, reject) => {
    // 使用 fork 方法启动子进程
    const translatorProcess = fork(translatorScriptPath, [gameName], { 
      stdio: ['pipe', 'pipe', 'pipe', 'ipc'],
      env: { 
        ...process.env, 
        LANG: 'zh_CN.UTF-8', 
        LC_ALL: 'zh_CN.UTF-8', 
        LC_CTYPE: 'zh_CN.UTF-8'
      }
    });

    console.log('[Main Process] 已创建翻译子进程');

    translatorProcess.stdout?.on('data', (data) => {
      try {
        const output = data.toString('utf8');
        console.log(`[Translator stdout] ${output}`);
      } catch (err) {
        console.error('[Main Process] 处理翻译进程输出时出错:', err);
      }
    });
    
    translatorProcess.stderr?.on('data', (data) => {
      try {
        const output = data.toString('utf8');
        console.error(`[Translator stderr] ${output}`);
      } catch (err) {
        console.error('[Main Process] 处理翻译进程错误输出时出错:', err);
      }
    });

    // 设置超时
    const translatorTimeout = setTimeout(() => {
      if (translatorProcess) {
        console.log('[Main Process] 翻译进程运行超时，强制终止');
        try {
          translatorProcess.kill();
        } catch (err) {
          console.error('[Main Process] 终止翻译进程时出错:', err);
        }
        resolve(null);
      }
    }, 15000); // 15秒超时

    // 监听来自翻译进程的消息
    translatorProcess.on('message', (message: { success: boolean; translatedName?: string; error?: string }) => {
      // 收到消息，清除超时计时器
      clearTimeout(translatorTimeout);
      
      console.log('[Main Process] 收到翻译结果:', message);
      
      if (message.success && message.translatedName) {
        resolve(message.translatedName);
      } else {
        resolve(null);
      }
      
      // 关闭进程
      try {
        translatorProcess.kill();
      } catch (err) {
        console.error('[Main Process] 关闭翻译进程时出错:', err);
      }
    });

    translatorProcess.on('exit', (code) => {
      console.log(`[Main Process] 翻译进程退出，退出码: ${code}`);
      clearTimeout(translatorTimeout);
      
      if (code !== 0) {
        resolve(null);
      }
    });
    
    // 监听错误事件
    translatorProcess.on('error', (err) => {
      console.error('[Main Process] 翻译进程出错:', err);
      clearTimeout(translatorTimeout);
      resolve(null);
    });
  });
} 