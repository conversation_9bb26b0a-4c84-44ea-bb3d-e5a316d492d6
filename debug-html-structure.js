const puppeteer = require('puppeteer');
const fs = require('fs');

async function analyzeHTMLStructure() {
  console.log('🔍 分析 FlingTrainer 网站的HTML结构...');
  
  const browser = await puppeteer.launch({
    headless: false, // 显示浏览器以便调试
    defaultViewport: { width: 1920, height: 1080 }
  });
  
  const page = await browser.newPage();
  
  try {
    console.log('📄 访问网站...');
    await page.goto('https://flingtrainer.com', {
      waitUntil: 'networkidle2',
      timeout: 30000
    });
    
    console.log('🔍 分析文章结构...');
    
    // 获取第一个文章的HTML结构
    const articleHTML = await page.evaluate(() => {
      const articles = document.querySelectorAll('article, .post, .entry, .trainer-item');
      if (articles.length > 0) {
        return articles[0].outerHTML;
      }
      return null;
    });
    
    if (articleHTML) {
      // 保存HTML到文件
      fs.writeFileSync('article-structure.html', articleHTML, 'utf8');
      console.log('✅ 文章HTML结构已保存到 article-structure.html');
      
      // 分析时间相关的元素
      const timeElements = await page.evaluate(() => {
        const articles = document.querySelectorAll('article, .post, .entry, .trainer-item');
        const timeInfo = [];
        
        if (articles.length > 0) {
          const article = articles[0];
          
          // 查找所有可能包含时间的元素
          const timeSelectors = [
            'time', '.time', '.date', '.post-date', '.entry-date', 
            '.published', '.updated', '.meta-date', '.post-meta',
            '[datetime]', '.meta', '.entry-meta'
          ];
          
          timeSelectors.forEach(selector => {
            const elements = article.querySelectorAll(selector);
            elements.forEach((el, index) => {
              timeInfo.push({
                selector: selector,
                index: index,
                textContent: el.textContent?.trim(),
                innerHTML: el.innerHTML,
                attributes: Array.from(el.attributes).map(attr => ({
                  name: attr.name,
                  value: attr.value
                }))
              });
            });
          });
        }
        
        return timeInfo;
      });
      
      console.log('⏰ 找到的时间相关元素:');
      timeElements.forEach((el, i) => {
        console.log(`${i + 1}. 选择器: ${el.selector}`);
        console.log(`   文本内容: "${el.textContent}"`);
        console.log(`   HTML: ${el.innerHTML.substring(0, 100)}...`);
        console.log(`   属性: ${JSON.stringify(el.attributes)}`);
        console.log('---');
      });
      
      // 保存时间元素信息到文件
      fs.writeFileSync('time-elements.json', JSON.stringify(timeElements, null, 2), 'utf8');
      console.log('✅ 时间元素信息已保存到 time-elements.json');
    }
    
    console.log('🎯 请查看生成的文件来分析正确的选择器');
    
  } catch (error) {
    console.error('❌ 分析过程中发生错误:', error);
  } finally {
    await browser.close();
  }
}

analyzeHTMLStructure();
