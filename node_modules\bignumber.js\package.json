{"name": "bignumber.js", "description": "A library for arbitrary-precision decimal and non-decimal arithmetic", "version": "2.4.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "repository": {"type": "git", "url": "https://github.com/MikeMcl/bignumber.js.git"}, "main": "bignumber.js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": "*"}, "license": "MIT", "scripts": {"test": "node ./test/every-test.js", "build": "uglifyjs bignumber.js --source-map bignumber.js.map -c -m -o bignumber.min.js --preamble \"/* bignumber.js v2.4.0 https://github.com/MikeMcl/bignumber.js/LICENCE */\""}}