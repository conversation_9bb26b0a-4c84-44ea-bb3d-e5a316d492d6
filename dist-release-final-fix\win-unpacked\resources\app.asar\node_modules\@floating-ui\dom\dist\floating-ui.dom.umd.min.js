!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("@floating-ui/core")):"function"==typeof define&&define.amd?define(["exports","@floating-ui/core"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).FloatingUIDOM={},t.FloatingUICore)}(this,(function(t,e){"use strict";const n=Math.min,o=Math.max,i=Math.round,r=Math.floor,c=t=>({x:t,y:t});function l(){return"undefined"!=typeof window}function s(t){return a(t)?(t.nodeName||"").toLowerCase():"#document"}function f(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function u(t){var e;return null==(e=(a(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function a(t){return!!l()&&(t instanceof Node||t instanceof f(t).Node)}function d(t){return!!l()&&(t instanceof Element||t instanceof f(t).Element)}function h(t){return!!l()&&(t instanceof HTMLElement||t instanceof f(t).HTMLElement)}function p(t){return!(!l()||"undefined"==typeof ShadowRoot)&&(t instanceof ShadowRoot||t instanceof f(t).ShadowRoot)}const g=new Set(["inline","contents"]);function m(t){const{overflow:e,overflowX:n,overflowY:o,display:i}=F(t);return/auto|scroll|overlay|hidden|clip/.test(e+o+n)&&!g.has(i)}const y=new Set(["table","td","th"]);function w(t){return y.has(s(t))}const x=[":popover-open",":modal"];function v(t){return x.some((e=>{try{return t.matches(e)}catch(t){return!1}}))}const b=["transform","translate","scale","rotate","perspective"],T=["transform","translate","scale","rotate","perspective","filter"],L=["paint","layout","strict","content"];function R(t){const e=S(),n=d(t)?F(t):t;return b.some((t=>!!n[t]&&"none"!==n[t]))||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||T.some((t=>(n.willChange||"").includes(t)))||L.some((t=>(n.contain||"").includes(t)))}function S(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}const C=new Set(["html","body","#document"]);function E(t){return C.has(s(t))}function F(t){return f(t).getComputedStyle(t)}function O(t){return d(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function D(t){if("html"===s(t))return t;const e=t.assignedSlot||t.parentNode||p(t)&&t.host||u(t);return p(e)?e.host:e}function H(t){const e=D(t);return E(e)?t.ownerDocument?t.ownerDocument.body:t.body:h(e)&&m(e)?e:H(e)}function P(t,e,n){var o;void 0===e&&(e=[]),void 0===n&&(n=!0);const i=H(t),r=i===(null==(o=t.ownerDocument)?void 0:o.body),c=f(i);if(r){const t=W(c);return e.concat(c,c.visualViewport||[],m(i)?i:[],t&&n?P(t):[])}return e.concat(i,P(i,[],n))}function W(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function M(t){const e=F(t);let n=parseFloat(e.width)||0,o=parseFloat(e.height)||0;const r=h(t),c=r?t.offsetWidth:n,l=r?t.offsetHeight:o,s=i(n)!==c||i(o)!==l;return s&&(n=c,o=l),{width:n,height:o,$:s}}function z(t){return d(t)?t:t.contextElement}function A(t){const e=z(t);if(!h(e))return c(1);const n=e.getBoundingClientRect(),{width:o,height:r,$:l}=M(e);let s=(l?i(n.width):n.width)/o,f=(l?i(n.height):n.height)/r;return s&&Number.isFinite(s)||(s=1),f&&Number.isFinite(f)||(f=1),{x:s,y:f}}const B=c(0);function V(t){const e=f(t);return S()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:B}function N(t,n,o,i){void 0===n&&(n=!1),void 0===o&&(o=!1);const r=t.getBoundingClientRect(),l=z(t);let s=c(1);n&&(i?d(i)&&(s=A(i)):s=A(t));const u=function(t,e,n){return void 0===e&&(e=!1),!(!n||e&&n!==f(t))&&e}(l,o,i)?V(l):c(0);let a=(r.left+u.x)/s.x,h=(r.top+u.y)/s.y,p=r.width/s.x,g=r.height/s.y;if(l){const t=f(l),e=i&&d(i)?f(i):i;let n=t,o=W(n);for(;o&&i&&e!==n;){const t=A(o),e=o.getBoundingClientRect(),i=F(o),r=e.left+(o.clientLeft+parseFloat(i.paddingLeft))*t.x,c=e.top+(o.clientTop+parseFloat(i.paddingTop))*t.y;a*=t.x,h*=t.y,p*=t.x,g*=t.y,a+=r,h+=c,n=f(o),o=W(n)}}return e.rectToClientRect({width:p,height:g,x:a,y:h})}function I(t,e){const n=O(t).scrollLeft;return e?e.left+n:N(u(t)).left+n}function k(t,e,n){void 0===n&&(n=!1);const o=t.getBoundingClientRect();return{x:o.left+e.scrollLeft-(n?0:I(t,o)),y:o.top+e.scrollTop}}const q=new Set(["absolute","fixed"]);function U(t,n,i){let r;if("viewport"===n)r=function(t,e){const n=f(t),o=u(t),i=n.visualViewport;let r=o.clientWidth,c=o.clientHeight,l=0,s=0;if(i){r=i.width,c=i.height;const t=S();(!t||t&&"fixed"===e)&&(l=i.offsetLeft,s=i.offsetTop)}return{width:r,height:c,x:l,y:s}}(t,i);else if("document"===n)r=function(t){const e=u(t),n=O(t),i=t.ownerDocument.body,r=o(e.scrollWidth,e.clientWidth,i.scrollWidth,i.clientWidth),c=o(e.scrollHeight,e.clientHeight,i.scrollHeight,i.clientHeight);let l=-n.scrollLeft+I(t);const s=-n.scrollTop;return"rtl"===F(i).direction&&(l+=o(e.clientWidth,i.clientWidth)-r),{width:r,height:c,x:l,y:s}}(u(t));else if(d(n))r=function(t,e){const n=N(t,!0,"fixed"===e),o=n.top+t.clientTop,i=n.left+t.clientLeft,r=h(t)?A(t):c(1);return{width:t.clientWidth*r.x,height:t.clientHeight*r.y,x:i*r.x,y:o*r.y}}(n,i);else{const e=V(t);r={x:n.x-e.x,y:n.y-e.y,width:n.width,height:n.height}}return e.rectToClientRect(r)}function j(t,e){const n=D(t);return!(n===e||!d(n)||E(n))&&("fixed"===F(n).position||j(n,e))}function X(t,e,n){const o=h(e),i=u(e),r="fixed"===n,l=N(t,!0,r,e);let f={scrollLeft:0,scrollTop:0};const a=c(0);function d(){a.x=I(i)}if(o||!o&&!r)if(("body"!==s(e)||m(i))&&(f=O(e)),o){const t=N(e,!0,r,e);a.x=t.x+e.clientLeft,a.y=t.y+e.clientTop}else i&&d();r&&!o&&i&&d();const p=!i||o||r?c(0):k(i,f);return{x:l.left+f.scrollLeft-a.x-p.x,y:l.top+f.scrollTop-a.y-p.y,width:l.width,height:l.height}}function Y(t){return"static"===F(t).position}function $(t,e){if(!h(t)||"fixed"===F(t).position)return null;if(e)return e(t);let n=t.offsetParent;return u(t)===n&&(n=n.ownerDocument.body),n}function _(t,e){const n=f(t);if(v(t))return n;if(!h(t)){let e=D(t);for(;e&&!E(e);){if(d(e)&&!Y(e))return e;e=D(e)}return n}let o=$(t,e);for(;o&&w(o)&&Y(o);)o=$(o,e);return o&&E(o)&&Y(o)&&!R(o)?n:o||function(t){let e=D(t);for(;h(e)&&!E(e);){if(R(e))return e;if(v(e))return null;e=D(e)}return null}(t)||n}const G={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:o,strategy:i}=t;const r="fixed"===i,l=u(o),f=!!e&&v(e.floating);if(o===l||f&&r)return n;let a={scrollLeft:0,scrollTop:0},d=c(1);const p=c(0),g=h(o);if((g||!g&&!r)&&(("body"!==s(o)||m(l))&&(a=O(o)),h(o))){const t=N(o);d=A(o),p.x=t.x+o.clientLeft,p.y=t.y+o.clientTop}const y=!l||g||r?c(0):k(l,a,!0);return{width:n.width*d.x,height:n.height*d.y,x:n.x*d.x-a.scrollLeft*d.x+p.x+y.x,y:n.y*d.y-a.scrollTop*d.y+p.y+y.y}},getDocumentElement:u,getClippingRect:function(t){let{element:e,boundary:i,rootBoundary:r,strategy:c}=t;const l=[..."clippingAncestors"===i?v(e)?[]:function(t,e){const n=e.get(t);if(n)return n;let o=P(t,[],!1).filter((t=>d(t)&&"body"!==s(t))),i=null;const r="fixed"===F(t).position;let c=r?D(t):t;for(;d(c)&&!E(c);){const e=F(c),n=R(c);n||"fixed"!==e.position||(i=null),(r?!n&&!i:!n&&"static"===e.position&&i&&q.has(i.position)||m(c)&&!n&&j(t,c))?o=o.filter((t=>t!==c)):i=e,c=D(c)}return e.set(t,o),o}(e,this._c):[].concat(i),r],f=l[0],u=l.reduce(((t,i)=>{const r=U(e,i,c);return t.top=o(r.top,t.top),t.right=n(r.right,t.right),t.bottom=n(r.bottom,t.bottom),t.left=o(r.left,t.left),t}),U(e,f,c));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:_,getElementRects:async function(t){const e=this.getOffsetParent||_,n=this.getDimensions,o=await n(t.floating);return{reference:X(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}},getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){const{width:e,height:n}=M(t);return{width:e,height:n}},getScale:A,isElement:d,isRTL:function(t){return"rtl"===F(t).direction}};function J(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}const K=e.detectOverflow,Q=e.offset,Z=e.autoPlacement,tt=e.shift,et=e.flip,nt=e.size,ot=e.hide,it=e.arrow,rt=e.inline,ct=e.limitShift;t.arrow=it,t.autoPlacement=Z,t.autoUpdate=function(t,e,i,c){void 0===c&&(c={});const{ancestorScroll:l=!0,ancestorResize:s=!0,elementResize:f="function"==typeof ResizeObserver,layoutShift:a="function"==typeof IntersectionObserver,animationFrame:d=!1}=c,h=z(t),p=l||s?[...h?P(h):[],...P(e)]:[];p.forEach((t=>{l&&t.addEventListener("scroll",i,{passive:!0}),s&&t.addEventListener("resize",i)}));const g=h&&a?function(t,e){let i,c=null;const l=u(t);function s(){var t;clearTimeout(i),null==(t=c)||t.disconnect(),c=null}return function f(u,a){void 0===u&&(u=!1),void 0===a&&(a=1),s();const d=t.getBoundingClientRect(),{left:h,top:p,width:g,height:m}=d;if(u||e(),!g||!m)return;const y={rootMargin:-r(p)+"px "+-r(l.clientWidth-(h+g))+"px "+-r(l.clientHeight-(p+m))+"px "+-r(h)+"px",threshold:o(0,n(1,a))||1};let w=!0;function x(e){const n=e[0].intersectionRatio;if(n!==a){if(!w)return f();n?f(!1,n):i=setTimeout((()=>{f(!1,1e-7)}),1e3)}1!==n||J(d,t.getBoundingClientRect())||f(),w=!1}try{c=new IntersectionObserver(x,{...y,root:l.ownerDocument})}catch(t){c=new IntersectionObserver(x,y)}c.observe(t)}(!0),s}(h,i):null;let m,y=-1,w=null;f&&(w=new ResizeObserver((t=>{let[n]=t;n&&n.target===h&&w&&(w.unobserve(e),cancelAnimationFrame(y),y=requestAnimationFrame((()=>{var t;null==(t=w)||t.observe(e)}))),i()})),h&&!d&&w.observe(h),w.observe(e));let x=d?N(t):null;return d&&function e(){const n=N(t);x&&!J(x,n)&&i();x=n,m=requestAnimationFrame(e)}(),i(),()=>{var t;p.forEach((t=>{l&&t.removeEventListener("scroll",i),s&&t.removeEventListener("resize",i)})),null==g||g(),null==(t=w)||t.disconnect(),w=null,d&&cancelAnimationFrame(m)}},t.computePosition=(t,n,o)=>{const i=new Map,r={platform:G,...o},c={...r.platform,_c:i};return e.computePosition(t,n,{...r,platform:c})},t.detectOverflow=K,t.flip=et,t.getOverflowAncestors=P,t.hide=ot,t.inline=rt,t.limitShift=ct,t.offset=Q,t.platform=G,t.shift=tt,t.size=nt}));
