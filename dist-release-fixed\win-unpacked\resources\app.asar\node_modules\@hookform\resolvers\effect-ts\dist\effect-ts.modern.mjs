import{toNestErrors as r,validateFieldsNatively as e}from"@hookform/resolvers";import{Effect as o}from"effect";import{decodeUnknown as s,ArrayFormatter as t}from"effect/ParseResult";const a=(a,m={errors:"all",onExcessProperty:"ignore"})=>(c,i,l)=>s(a,m)(c).pipe(o.catchAll(r=>o.flip(t.formatIssue(r))),o.mapError(e=>{const o=e.reduce((r,e)=>(r[e.path.join(".")]={message:e.message,type:e._tag},r),{});return r(o,l)}),o.tap(()=>o.sync(()=>l.shouldUseNativeValidation&&e({},l))),o.match({onFailure:r=>({errors:r,values:{}}),onSuccess:r=>({errors:{},values:r})}),o.runPromise);export{a as effectTsResolver};
//# sourceMappingURL=effect-ts.modern.mjs.map
