{"name": "is-plain-object", "description": "Returns true if an object was created by the `Object` constructor.", "version": "2.0.4", "homepage": "https://github.com/jonschlinkert/is-plain-object", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON> (http://twitter.com/jonschlink<PERSON>)", "<PERSON><PERSON> (http://onokumus.com)", "<PERSON> (https://svachon.com)", "(https://github.com/wtgtybhertgeghgtwtg)"], "repository": "jonschlinkert/is-plain-object", "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "license": "MIT", "files": ["index.d.ts", "index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"browserify": "browserify index.js --standalone isPlainObject | uglifyjs --compress --mangle -o browser/is-plain-object.js", "test_browser": "mocha-phantomjs test/browser.html", "test_node": "mocha", "test": "npm run test_node && npm run browserify && npm run test_browser"}, "dependencies": {"isobject": "^3.0.1"}, "devDependencies": {"browserify": "^14.4.0", "chai": "^4.0.2", "gulp-format-md": "^1.0.0", "mocha": "^3.4.2", "mocha-phantomjs": "^4.1.0", "phantomjs": "^2.1.7", "uglify-js": "^3.0.24"}, "keywords": ["check", "is", "is-object", "isobject", "javascript", "kind", "kind-of", "object", "plain", "type", "typeof", "value"], "types": "index.d.ts", "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-number", "isobject", "kind-of"]}, "lint": {"reflinks": true}}}