

> @jimp/js-png@1.1.2 test:browser /Users/<USER>/Documents/jimp/plugins/js-png
> vitest --config vitest.config.browser.mjs "--watch=false"

Re-optimizing dependencies because lockfile has changed
Port 5173 is in use, trying another one...
Port 5174 is in use, trying another one...

[7m[1m[36m RUN [39m[22m[27m [36mv2.0.5[39m [90m/Users/<USER>/Documents/jimp/plugins/js-png[39m
[2m[32m      Browser runner started at http://localhost:5175/[39m[22m

[2m1:39:22 AM[22m [36m[1m[vite][22m[39m [32m✨ new dependencies optimized: [33mvite-plugin-node-polyfills/shims/buffer, vite-plugin-node-polyfills/shims/global, vite-plugin-node-polyfills/shims/process, path[32m[39m
[2m1:39:22 AM[22m [36m[1m[vite][22m[39m [32m✨ optimized dependencies changed. reloading[39m
[?25l [90m·[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
   [90m·[39m PNG[2m (2)[22m
     [90m·[39m load PNG
     [90m·[39m export PNG
[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
   [33m❯[39m PNG[2m (2)[22m
     [33m⠙[39m load PNG
     [90m·[39m export PNG
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
   [33m❯[39m PNG[2m (2)[22m
     [33m⠹[39m load PNG
     [90m·[39m export PNG
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
   [33m❯[39m PNG[2m (2)[22m
     [33m⠸[39m load PNG
     [90m·[39m export PNG
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
   [33m❯[39m PNG[2m (2)[22m
     [33m⠼[39m load PNG
     [90m·[39m export PNG
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
   [33m❯[39m PNG[2m (2)[22m
     [33m⠴[39m load PNG
     [90m·[39m export PNG
[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
   [32m✓[39m PNG[2m (2)[22m
     [32m✓[39m load PNG
     [32m✓[39m export PNG

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m2 passed[39m[22m[90m (2)[39m
[2m   Start at [22m 01:39:13
[2m   Duration [22m 11.93s[2m (transform 0ms, setup 0ms, collect 560ms, tests 91ms, environment 0ms, prepare 1.57s)[22m

[?25h[?25h
