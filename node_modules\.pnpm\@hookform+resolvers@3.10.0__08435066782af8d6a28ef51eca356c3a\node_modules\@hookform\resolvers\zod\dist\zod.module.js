import{validateFieldsNatively as r,toNestErrors as e}from"@hookform/resolvers";import{appendErrors as o}from"react-hook-form";var n=function(r,e){for(var n={};r.length;){var t=r[0],s=t.code,i=t.message,a=t.path.join(".");if(!n[a])if("unionErrors"in t){var u=t.unionErrors[0].errors[0];n[a]={message:u.message,type:u.code}}else n[a]={message:i,type:s};if("unionErrors"in t&&t.unionErrors.forEach(function(e){return e.errors.forEach(function(e){return r.push(e)})}),e){var c=n[a].types,f=c&&c[t.code];n[a]=o(a,e,n,s,f?[].concat(f,t.message):t.message)}r.shift()}return n},t=function(o,t,s){return void 0===s&&(s={}),function(i,a,u){try{return Promise.resolve(function(e,n){try{var a=Promise.resolve(o["sync"===s.mode?"parse":"parseAsync"](i,t)).then(function(e){return u.shouldUseNativeValidation&&r({},u),{errors:{},values:s.raw?i:e}})}catch(r){return n(r)}return a&&a.then?a.then(void 0,n):a}(0,function(r){if(function(r){return Array.isArray(null==r?void 0:r.errors)}(r))return{values:{},errors:e(n(r.errors,!u.shouldUseNativeValidation&&"all"===u.criteriaMode),u)};throw r}))}catch(r){return Promise.reject(r)}}};export{t as zodResolver};
//# sourceMappingURL=zod.module.js.map
