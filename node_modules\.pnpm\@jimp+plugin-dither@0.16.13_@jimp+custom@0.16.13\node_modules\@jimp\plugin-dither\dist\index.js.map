{"version": 3, "sources": ["../src/index.js"], "names": ["dither", "cb", "rgb565Matrix", "scanQuiet", "bitmap", "width", "height", "x", "y", "idx", "thresholdId", "data", "Math", "min", "call", "dither565", "dither16"], "mappings": ";;;;;;;AAAA;;AAEA;;;;;AAKA,SAASA,MAAT,CAAgBC,EAAhB,EAAoB;AAClB,MAAMC,YAAY,GAAG,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,EAAV,EAAc,EAAd,EAAkB,CAAlB,EAAqB,EAArB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,EAA/B,EAAmC,CAAnC,EAAsC,EAAtC,EAA0C,EAA1C,EAA8C,CAA9C,EAAiD,EAAjD,EAAqD,CAArD,CAArB;AACA,OAAKC,SAAL,CACE,CADF,EAEE,CAFF,EAGE,KAAKC,MAAL,CAAYC,KAHd,EAIE,KAAKD,MAAL,CAAYE,MAJd,EAKE,UAAUC,CAAV,EAAaC,CAAb,EAAgBC,GAAhB,EAAqB;AACnB,QAAMC,WAAW,GAAG,CAAC,CAACF,CAAC,GAAG,CAAL,KAAW,CAAZ,IAAkBD,CAAC,GAAG,CAA1C;AACA,QAAMP,MAAM,GAAGE,YAAY,CAACQ,WAAD,CAA3B;AACA,SAAKN,MAAL,CAAYO,IAAZ,CAAiBF,GAAjB,IAAwBG,IAAI,CAACC,GAAL,CAAS,KAAKT,MAAL,CAAYO,IAAZ,CAAiBF,GAAjB,IAAwBT,MAAjC,EAAyC,IAAzC,CAAxB;AACA,SAAKI,MAAL,CAAYO,IAAZ,CAAiBF,GAAG,GAAG,CAAvB,IAA4BG,IAAI,CAACC,GAAL,CAC1B,KAAKT,MAAL,CAAYO,IAAZ,CAAiBF,GAAG,GAAG,CAAvB,IAA4BT,MADF,EAE1B,IAF0B,CAA5B;AAIA,SAAKI,MAAL,CAAYO,IAAZ,CAAiBF,GAAG,GAAG,CAAvB,IAA4BG,IAAI,CAACC,GAAL,CAC1B,KAAKT,MAAL,CAAYO,IAAZ,CAAiBF,GAAG,GAAG,CAAvB,IAA4BT,MADF,EAE1B,IAF0B,CAA5B;AAID,GAjBH;;AAoBA,MAAI,0BAAcC,EAAd,CAAJ,EAAuB;AACrBA,IAAAA,EAAE,CAACa,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,SAAO,IAAP;AACD;;eAEc;AAAA,SAAO;AACpBC,IAAAA,SAAS,EAAEf,MADS;AAEpBgB,IAAAA,QAAQ,EAAEhB;AAFU,GAAP;AAAA,C", "sourcesContent": ["import { isNodePattern } from \"@jimp/utils\";\n\n/**\n * Apply a ordered dithering effect\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nfunction dither(cb) {\n  const rgb565Matrix = [1, 9, 3, 11, 13, 5, 15, 7, 4, 12, 2, 10, 16, 8, 14, 6];\n  this.scanQuiet(\n    0,\n    0,\n    this.bitmap.width,\n    this.bitmap.height,\n    function (x, y, idx) {\n      const thresholdId = ((y & 3) << 2) + (x % 4);\n      const dither = rgb565Matrix[thresholdId];\n      this.bitmap.data[idx] = Math.min(this.bitmap.data[idx] + dither, 0xff);\n      this.bitmap.data[idx + 1] = Math.min(\n        this.bitmap.data[idx + 1] + dither,\n        0xff\n      );\n      this.bitmap.data[idx + 2] = Math.min(\n        this.bitmap.data[idx + 2] + dither,\n        0xff\n      );\n    }\n  );\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n}\n\nexport default () => ({\n  dither565: dither,\n  dither16: dither,\n});\n"], "file": "index.js"}