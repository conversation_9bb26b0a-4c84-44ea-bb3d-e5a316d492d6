!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("@hookform/resolvers"),require("class-transformer"),require("class-validator")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers","class-transformer","class-validator"],r):r((e||self).hookformResolversClassValidator={},e.hookformResolvers,e.classTransformer,e.classValidator)}(this,function(e,r,o,s){var t=function e(r,o,s,t){return void 0===s&&(s={}),void 0===t&&(t=""),r.reduce(function(r,s){var a=t?t+"."+s.property:s.property;if(s.constraints){var i=Object.keys(s.constraints)[0];r[a]={type:i,message:s.constraints[i]};var n=r[a];o&&n&&Object.assign(n,{types:s.constraints})}return s.children&&s.children.length&&e(s.children,o,r,a),r},s)};e.classValidatorResolver=function(e,a,i){return void 0===a&&(a={}),void 0===i&&(i={}),function(n,l,d){try{var c=a.validator,f=o.plainToClass(e,n,a.transformer);return Promise.resolve(("sync"===i.mode?s.validateSync:s.validate)(f,c)).then(function(e){return e.length?{values:{},errors:r.toNestErrors(t(e,!d.shouldUseNativeValidation&&"all"===d.criteriaMode),d)}:(d.shouldUseNativeValidation&&r.validateFieldsNatively({},d),{values:i.rawValues?n:f,errors:{}})})}catch(e){return Promise.reject(e)}}}});
//# sourceMappingURL=class-validator.umd.js.map
