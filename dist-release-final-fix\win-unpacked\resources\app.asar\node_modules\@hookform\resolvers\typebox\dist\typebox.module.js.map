{"version": 3, "file": "typebox.module.js", "sources": ["../src/typebox.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { TypeCheck } from '@sinclair/typebox/compiler';\nimport { Value, type ValueError } from '@sinclair/typebox/value';\nimport { FieldError, FieldErrors, appendErrors } from 'react-hook-form';\nimport type { Resolver } from './types';\n\nconst parseErrorSchema = (\n  _errors: ValueError[],\n  validateAllFieldCriteria: boolean,\n) => {\n  const errors: Record<string, FieldError> = {};\n  for (; _errors.length; ) {\n    const error = _errors[0];\n    const { type, message, path } = error;\n    const _path = path.substring(1).replace(/\\//g, '.');\n\n    if (!errors[_path]) {\n      errors[_path] = { message, type: '' + type };\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = errors[_path].types;\n      const messages = types && types['' + type];\n\n      errors[_path] = appendErrors(\n        _path,\n        validateAllFieldCriteria,\n        errors,\n        '' + type,\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message)\n          : error.message,\n      ) as FieldError;\n    }\n\n    _errors.shift();\n  }\n\n  return errors;\n};\n\nexport const typeboxResolver: Resolver =\n  (schema) => async (values, _, options) => {\n    const errors = Array.from(\n      schema instanceof TypeCheck\n        ? schema.Errors(values)\n        : Value.Errors(schema, values),\n    );\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    if (!errors.length) {\n      return {\n        errors: {} as FieldErrors,\n        values,\n      };\n    }\n\n    return {\n      values: {},\n      errors: toNestErrors(\n        parseErrorSchema(\n          errors,\n          !options.shouldUseNativeValidation && options.criteriaMode === 'all',\n        ),\n        options,\n      ),\n    };\n  };\n"], "names": ["parseErrorSchema", "_errors", "validateAllFieldCriteria", "errors", "length", "error", "type", "message", "_path", "path", "substring", "replace", "types", "messages", "appendErrors", "concat", "shift", "typeboxResolver", "schema", "values", "_", "options", "Array", "from", "TypeCheck", "Errors", "Value", "shouldUseNativeValidation", "validateFieldsNatively", "Promise", "resolve", "toNestErrors", "criteriaMode", "e", "reject"], "mappings": "qOAMA,IAAMA,EAAmB,SACvBC,EACAC,GAGA,IADA,IAAMC,EAAqC,GACpCF,EAAQG,QAAU,CACvB,IAAMC,EAAQJ,EAAQ,GACdK,EAAwBD,EAAxBC,KAAMC,EAAkBF,EAAlBE,QACRC,EAD0BH,EAATI,KACJC,UAAU,GAAGC,QAAQ,MAAO,KAM/C,GAJKR,EAAOK,KACVL,EAAOK,GAAS,CAAED,QAAAA,EAASD,KAAM,GAAKA,IAGpCJ,EAA0B,CAC5B,IAAMU,EAAQT,EAAOK,GAAOI,MACtBC,EAAWD,GAASA,EAAM,GAAKN,GAErCH,EAAOK,GAASM,EACdN,EACAN,EACAC,EACA,GAAKG,EACLO,EACK,GAAgBE,OAAOF,EAAsBR,EAAME,SACpDF,EAAME,QAEd,CAEAN,EAAQe,OACV,CAEA,OAAOb,CACT,EAEac,EACX,SAACC,GAAkBC,OAAAA,SAAAA,EAAQC,EAAGC,GAAO,IACnC,IAAMlB,EAASmB,MAAMC,KACnBL,aAAkBM,EACdN,EAAOO,OAAON,GACdO,EAAMD,OAAOP,EAAQC,IAK3B,OAFAE,EAAQM,2BAA6BC,EAAuB,GAAIP,GAShEQ,QAAAC,QAPK3B,EAAOC,OAOL,CACLe,OAAQ,CAAA,EACRhB,OAAQ4B,EACN/B,EACEG,GACCkB,EAAQM,2BAAsD,QAAzBN,EAAQW,cAEhDX,IAbK,CACLlB,OAAQ,CAAA,EACRgB,OAAAA,GAcN,CAAC,MAAAc,GAAA,OAAAJ,QAAAK,OAAAD,EAAA,CAAA,CAAA"}