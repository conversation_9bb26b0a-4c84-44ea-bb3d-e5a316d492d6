const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔍 便携版调试工具');
console.log('==================\n');

const portablePath = 'dist-release-fixed-final/游戏修改器盒子-便携版.exe';

console.log('📋 便携版信息:');
console.log(`路径: ${portablePath}`);
console.log(`存在: ${fs.existsSync(portablePath) ? '✅' : '❌'}`);

if (fs.existsSync(portablePath)) {
  const stats = fs.statSync(portablePath);
  console.log(`大小: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
  console.log(`修改时间: ${stats.mtime}`);
}

console.log('\n🚀 启动便携版进行详细调试...');

function debugPortableApp() {
  return new Promise((resolve) => {
    const app = spawn(portablePath, [], {
      stdio: ['pipe', 'pipe', 'pipe'],
      detached: false,
      env: {
        ...process.env,
        ELECTRON_ENABLE_LOGGING: '1',
        ELECTRON_LOG_ASAR_READS: '1',
        DEBUG: '*'
      }
    });
    
    let hasAnyOutput = false;
    let stdout = '';
    let stderr = '';
    
    app.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      hasAnyOutput = true;
      console.log(`[STDOUT] ${output.trim()}`);
    });
    
    app.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      hasAnyOutput = true;
      console.log(`[STDERR] ${output.trim()}`);
    });
    
    app.on('spawn', () => {
      console.log('✅ 进程已启动');
    });
    
    app.on('close', (code, signal) => {
      console.log(`\n📊 便携版调试结果:`);
      console.log(`  退出代码: ${code}`);
      console.log(`  信号: ${signal}`);
      console.log(`  有输出: ${hasAnyOutput ? '是' : '否'}`);
      console.log(`  STDOUT长度: ${stdout.length}`);
      console.log(`  STDERR长度: ${stderr.length}`);
      
      if (stdout) {
        console.log('\n📝 STDOUT内容:');
        console.log(stdout);
      }
      
      if (stderr) {
        console.log('\n⚠️ STDERR内容:');
        console.log(stderr);
      }
      
      resolve({ code, signal, hasAnyOutput, stdout, stderr });
    });
    
    app.on('error', (err) => {
      console.log(`❌ 启动失败: ${err.message}`);
      resolve({ error: err.message });
    });
    
    // 10秒后主动关闭
    setTimeout(() => {
      if (!app.killed) {
        console.log('\n⏰ 10秒测试时间到，关闭应用...');
        app.kill('SIGTERM');
        
        // 如果SIGTERM不起作用，使用SIGKILL
        setTimeout(() => {
          if (!app.killed) {
            console.log('🔨 强制关闭应用...');
            app.kill('SIGKILL');
          }
        }, 2000);
      }
    }, 10000);
  });
}

// 运行调试
debugPortableApp().then(result => {
  console.log('\n🎯 调试完成');
  
  if (result.error) {
    console.log('❌ 便携版存在启动问题');
    console.log('💡 可能的原因:');
    console.log('  1. 缺少必要的DLL文件');
    console.log('  2. 权限问题');
    console.log('  3. 便携版打包配置问题');
  } else if (!result.hasAnyOutput) {
    console.log('⚠️ 便携版启动但无输出');
    console.log('💡 可能的原因:');
    console.log('  1. 应用程序静默运行');
    console.log('  2. 输出重定向问题');
    console.log('  3. 需要GUI交互');
  } else {
    console.log('✅ 便携版正常运行');
  }
  
  console.log('\n📍 建议:');
  console.log('  1. 手动双击便携版exe文件测试');
  console.log('  2. 检查是否有GUI窗口出现');
  console.log('  3. 查看任务管理器中的进程');
  console.log('  4. 如果有窗口，测试所有功能');
}).catch(console.error);
