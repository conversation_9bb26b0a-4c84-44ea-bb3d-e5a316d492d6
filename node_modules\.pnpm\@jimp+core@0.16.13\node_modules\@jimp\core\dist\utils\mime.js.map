{"version": 3, "sources": ["../../src/utils/mime.js"], "names": ["mimeTypes", "findType", "extension", "Object", "entries", "find", "type", "includes", "addType", "mime", "extensions", "getType", "path", "pathParts", "split", "slice", "length", "pop", "getExtension", "toLowerCase"], "mappings": ";;;;;;AAAA,IAAMA,SAAS,GAAG,EAAlB;;AAEA,IAAMC,QAAQ,GAAG,SAAXA,QAAW,CAACC,SAAD;AAAA,SACfC,MAAM,CAACC,OAAP,CAAeJ,SAAf,EAA0BK,IAA1B,CAA+B,UAACC,IAAD;AAAA,WAAUA,IAAI,CAAC,CAAD,CAAJ,CAAQC,QAAR,CAAiBL,SAAjB,CAAV;AAAA,GAA/B,KAAyE,EAD1D;AAAA,CAAjB;;AAGO,IAAMM,OAAO,GAAG,SAAVA,OAAU,CAACC,IAAD,EAAOC,UAAP,EAAsB;AAC3CV,EAAAA,SAAS,CAACS,IAAD,CAAT,GAAkBC,UAAlB;AACD,CAFM;AAIP;;;;;;;;;AAKO,IAAMC,OAAO,GAAG,SAAVA,OAAU,CAACC,IAAD,EAAU;AAC/B,MAAMC,SAAS,GAAGD,IAAI,CAACE,KAAL,CAAW,GAAX,EAAgBC,KAAhB,CAAsB,CAAC,CAAvB,CAAlB;AACA,MAAMb,SAAS,GAAGW,SAAS,CAACA,SAAS,CAACG,MAAV,GAAmB,CAApB,CAAT,CAAgCF,KAAhC,CAAsC,GAAtC,EAA2CG,GAA3C,EAAlB;AACA,MAAMX,IAAI,GAAGL,QAAQ,CAACC,SAAD,CAArB;AAEA,SAAOI,IAAI,CAAC,CAAD,CAAX;AACD,CANM;AAQP;;;;;;;;;AAKO,IAAMY,YAAY,GAAG,SAAfA,YAAe,CAACZ,IAAD;AAAA,SAAU,CAACN,SAAS,CAACM,IAAI,CAACa,WAAL,EAAD,CAAT,IAAiC,EAAlC,EAAsC,CAAtC,CAAV;AAAA,CAArB", "sourcesContent": ["const mimeTypes = {};\n\nconst findType = (extension) =>\n  Object.entries(mimeTypes).find((type) => type[1].includes(extension)) || [];\n\nexport const addType = (mime, extensions) => {\n  mimeTypes[mime] = extensions;\n};\n\n/**\n * Lookup a mime type based on extension\n * @param {string} path path to find extension for\n * @returns {string} mime found mime type\n */\nexport const getType = (path) => {\n  const pathParts = path.split(\"/\").slice(-1);\n  const extension = pathParts[pathParts.length - 1].split(\".\").pop();\n  const type = findType(extension);\n\n  return type[0];\n};\n\n/**\n * Return file extension associated with a mime type\n * @param {string} type mime type to look up\n * @returns {string} extension file extension\n */\nexport const getExtension = (type) => (mimeTypes[type.toLowerCase()] || [])[0];\n"], "file": "mime.js"}