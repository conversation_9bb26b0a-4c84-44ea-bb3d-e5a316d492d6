{"version": 3, "file": "typanion.modern.mjs", "sources": ["../src/typanion.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport type { FieldError, FieldErrors } from 'react-hook-form';\nimport type { Resolver } from './types';\n\nconst parseErrors = (errors: string[], parsedErrors: FieldErrors = {}) => {\n  return errors.reduce((acc, error) => {\n    const fieldIndex = error.indexOf(':');\n\n    const field = error.slice(1, fieldIndex);\n    const message = error.slice(fieldIndex + 1).trim();\n\n    acc[field] = {\n      message,\n    } as FieldError;\n\n    return acc;\n  }, parsedErrors);\n};\n\nexport const typanionResolver: Resolver =\n  (validator, validatorOptions = {}) =>\n  (values, _, options) => {\n    const rawErrors: string[] = [];\n    const isValid = validator(\n      values,\n      Object.assign(\n        {},\n        {\n          errors: rawErrors,\n        },\n        validatorOptions,\n      ),\n    );\n    const parsedErrors = parseErrors(rawErrors);\n\n    if (isValid) {\n      options.shouldUseNativeValidation &&\n        validateFieldsNatively(parsedErrors, options);\n\n      return { values, errors: {} };\n    }\n\n    return { values: {}, errors: toNestErrors(parsedErrors, options) };\n  };\n"], "names": ["typanionResolver", "validator", "validatorOptions", "values", "_", "options", "rawErrors", "<PERSON><PERSON><PERSON><PERSON>", "Object", "assign", "errors", "parsedErrors", "parseErrors", "reduce", "acc", "error", "fieldIndex", "indexOf", "field", "slice", "message", "trim", "shouldUseNativeValidation", "validateFieldsNatively", "toNestErrors"], "mappings": "+EAIA,MAeaA,EACXA,CAACC,EAAWC,EAAmB,CAAE,IACjC,CAACC,EAAQC,EAAGC,KACV,MAAMC,EAAsB,GACtBC,EAAUN,EACdE,EACAK,OAAOC,OACL,GACA,CACEC,OAAQJ,GAEVJ,IAGES,EA7BUC,EAACF,EAAkBC,EAA4B,CAAA,IAC1DD,EAAOG,OAAO,CAACC,EAAKC,KACzB,MAAMC,EAAaD,EAAME,QAAQ,KAE3BC,EAAQH,EAAMI,MAAM,EAAGH,GACvBI,EAAUL,EAAMI,MAAMH,EAAa,GAAGK,OAM5C,OAJAP,EAAII,GAAS,CACXE,WAGKN,GACNH,GAiBoBC,CAAYN,GAEjC,OAAIC,GACFF,EAAQiB,2BACNC,EAAuBZ,EAAcN,GAEhC,CAAEF,SAAQO,OAAQ,KAGpB,CAAEP,OAAQ,CAAA,EAAIO,OAAQc,EAAab,EAAcN"}