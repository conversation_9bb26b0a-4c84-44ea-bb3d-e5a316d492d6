import { spawn, fork, ChildProcess } from 'child_process';
import { app } from 'electron';
import * as path from 'path';
import * as fs from 'fs';

// 子进程管理器，处理开发环境和打包环境的差异
export class SubprocessManager {
  private static instance: SubprocessManager;

  public static getInstance(): SubprocessManager {
    if (!SubprocessManager.instance) {
      SubprocessManager.instance = new SubprocessManager();
    }
    return SubprocessManager.instance;
  }

  // 获取Node.js可执行文件路径
  private getNodeExecutablePath(): string {
    if (app.isPackaged) {
      console.log(`[SubprocessManager] 打包环境，使用Electron作为Node.js: ${process.execPath}`);
      return process.execPath;
    } else {
      console.log(`[SubprocessManager] 开发环境，使用系统Node.js: ${process.execPath}`);
      return process.execPath;
    }
  }

  // 获取脚本路径
  private getScriptPath(scriptName: string): string | null {
    let scriptPath: string;

    if (app.isPackaged) {
      console.log(`[SubprocessManager] 打包环境，查找脚本: ${scriptName}`);
      console.log(`[SubprocessManager] process.resourcesPath: ${process.resourcesPath}`);

      // 尝试多个可能的路径
      const possiblePaths = [
        path.join(process.resourcesPath, 'scraper', scriptName),
        path.join(process.resourcesPath, 'app.asar.unpacked', 'dist', 'scraper', scriptName),
        path.join(app.getAppPath(), 'resources', 'scraper', scriptName),
        path.join(__dirname, '..', 'scraper', scriptName)
      ];

      for (const testPath of possiblePaths) {
        console.log(`[SubprocessManager] 检查脚本路径: ${testPath}`);
        if (fs.existsSync(testPath)) {
          console.log(`[SubprocessManager] ✅ 找到脚本: ${testPath}`);
          return testPath;
        } else {
          console.log(`[SubprocessManager] ❌ 脚本不存在: ${testPath}`);
        }
      }

      console.error(`[SubprocessManager] ❌ 无法找到脚本: ${scriptName}`);
      return null;
    } else {
      // 开发环境
      scriptPath = path.join(app.getAppPath(), 'dist', 'scraper', scriptName);
      console.log(`[SubprocessManager] 开发环境脚本路径: ${scriptPath}`);

      if (fs.existsSync(scriptPath)) {
        return scriptPath;
      } else {
        console.error(`[SubprocessManager] 开发环境脚本不存在: ${scriptPath}`);
        return null;
      }
    }
  }

  // 启动子进程的通用方法
  public startSubprocess(scriptName: string, args: string[] = []): Promise<ChildProcess | null> {
    return new Promise((resolve, reject) => {
      const scriptPath = this.getScriptPath(scriptName);
      if (!scriptPath) {
        reject(new Error(`无法找到脚本: ${scriptName}`));
        return;
      }

      let childProcess: ChildProcess;

      if (app.isPackaged) {
        // 打包环境：使用spawn启动
        const nodeExecutable = this.getNodeExecutablePath();
        console.log(`[SubprocessManager] 使用Node.js路径: ${nodeExecutable}`);
        console.log(`[SubprocessManager] 脚本路径: ${scriptPath}`);
        console.log(`[SubprocessManager] 参数: ${args.join(', ')}`);

        childProcess = spawn(nodeExecutable, [scriptPath, ...args], {
          stdio: ['pipe', 'pipe', 'pipe'],
          env: {
            ...process.env,
            ELECTRON_RUN_AS_NODE: '1',
            LANG: 'zh_CN.UTF-8',
            LC_ALL: 'zh_CN.UTF-8',
            LC_CTYPE: 'zh_CN.UTF-8',
            NODE_OPTIONS: '--no-warnings --max-old-space-size=1024'
          }
        });
      } else {
        // 开发环境：使用fork方法
        childProcess = fork(scriptPath, args, {
          stdio: ['pipe', 'pipe', 'pipe', 'ipc'],
          env: {
            ...process.env,
            LANG: 'zh_CN.UTF-8',
            LC_ALL: 'zh_CN.UTF-8',
            LC_CTYPE: 'zh_CN.UTF-8'
          }
        });
      }

      // 添加错误处理
      childProcess.on('error', (error) => {
        console.error(`[SubprocessManager] 子进程启动失败:`, error);
        reject(error);
      });

      childProcess.on('spawn', () => {
        console.log(`[SubprocessManager] 子进程启动成功, PID: ${childProcess.pid}`);
        resolve(childProcess);
      });

      // 如果在一定时间内没有spawn事件，也认为启动成功
      setTimeout(() => {
        if (childProcess && !childProcess.killed) {
          resolve(childProcess);
        }
      }, 1000);
    });
  }

  // 专门用于翻译的方法
  public async startTranslator(gameName: string): Promise<any> {
    try {
      const childProcess = await this.startSubprocess('translator.js', [gameName]);
      if (!childProcess) {
        throw new Error('无法启动翻译进程');
      }

      return new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          if (childProcess) {
            console.log('翻译请求超时，终止进程');
            childProcess.kill();
          }
          reject(new Error('翻译超时'));
        }, 30000);

        let stdoutBuffer = '';

        childProcess.stdout?.on('data', (data) => {
          const output = data.toString();
          stdoutBuffer += output;
          console.log(`[Translator stdout] ${output}`);

          // 查找RESULT:开头的JSON数据
          const resultMatch = stdoutBuffer.match(/RESULT:(.+)/);
          if (resultMatch) {
            try {
              const result = JSON.parse(resultMatch[1]);
              console.log('[SubprocessManager] 翻译结果:', JSON.stringify(result));
              clearTimeout(timeoutId);
              childProcess.kill();
              resolve(result);
            } catch (parseErr) {
              console.error('[SubprocessManager] 解析翻译结果失败:', parseErr);
            }
          }
        });

        childProcess.stderr?.on('data', (data) => {
          console.error(`[Translator stderr] ${data.toString()}`);
        });

        childProcess.on('close', (code) => {
          clearTimeout(timeoutId);
          if (code !== 0 && code !== null) {
            reject(new Error(`翻译进程异常退出，代码: ${code}`));
          }
        });
      });
    } catch (error) {
      console.error('[SubprocessManager] 启动翻译器失败:', error);
      throw error;
    }
  }

  // 专门用于搜索的方法
  public async startSearchScraper(searchQuery: string): Promise<any> {
    try {
      const childProcess = await this.startSubprocess('search-scraper.js', [searchQuery]);
      if (!childProcess) {
        throw new Error('无法启动搜索进程');
      }

      return new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          if (childProcess) {
            console.log('搜索请求超时，终止进程');
            childProcess.kill();
          }
          reject(new Error('搜索超时'));
        }, 60000);

        let stdoutBuffer = '';

        childProcess.stdout?.on('data', (data) => {
          const output = data.toString();
          stdoutBuffer += output;
          console.log(`[Search stdout] ${output}`);

          // 查找RESULT:开头的JSON数据
          const resultMatch = stdoutBuffer.match(/RESULT:(.+)/);
          if (resultMatch) {
            try {
              const result = JSON.parse(resultMatch[1]);
              console.log('[SubprocessManager] 搜索结果:', JSON.stringify(result));
              clearTimeout(timeoutId);
              childProcess.kill();
              resolve(result);
            } catch (parseErr) {
              console.error('[SubprocessManager] 解析搜索结果失败:', parseErr);
            }
          }
        });

        childProcess.stderr?.on('data', (data) => {
          console.error(`[Search stderr] ${data.toString()}`);
        });

        childProcess.on('close', (code) => {
          clearTimeout(timeoutId);
          if (code !== 0 && code !== null) {
            reject(new Error(`搜索进程异常退出，代码: ${code}`));
          }
        });
      });
    } catch (error) {
      console.error('[SubprocessManager] 启动搜索器失败:', error);
      throw error;
    }
  }

  // 专门用于详情抓取的方法
  public async startDetailScraper(trainerLink: string): Promise<any> {
    try {
      const childProcess = await this.startSubprocess('detail-scraper.js', [trainerLink]);
      if (!childProcess) {
        throw new Error('无法启动详情抓取进程');
      }

      return new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          if (childProcess) {
            console.log('详情抓取请求超时，终止进程');
            childProcess.kill();
          }
          reject(new Error('详情抓取超时'));
        }, 60000);

        let stdoutBuffer = '';

        childProcess.stdout?.on('data', (data) => {
          const output = data.toString();
          stdoutBuffer += output;
          console.log(`[Detail stdout] ${output}`);

          // 查找RESULT:开头的JSON数据
          const resultMatch = stdoutBuffer.match(/RESULT:(.+)/);
          if (resultMatch) {
            try {
              const result = JSON.parse(resultMatch[1]);
              console.log('[SubprocessManager] 详情抓取结果:', JSON.stringify(result));
              clearTimeout(timeoutId);
              childProcess.kill();
              resolve(result);
            } catch (parseErr) {
              console.error('[SubprocessManager] 解析详情抓取结果失败:', parseErr);
            }
          }
        });

        childProcess.stderr?.on('data', (data) => {
          console.error(`[Detail stderr] ${data.toString()}`);
        });

        childProcess.on('close', (code) => {
          clearTimeout(timeoutId);
          if (code !== 0 && code !== null) {
            reject(new Error(`详情抓取进程异常退出，代码: ${code}`));
          }
        });
      });
    } catch (error) {
      console.error('[SubprocessManager] 启动详情抓取器失败:', error);
      throw error;
    }
  }
}
