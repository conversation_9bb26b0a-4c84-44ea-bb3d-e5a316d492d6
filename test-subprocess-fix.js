const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// 模拟打包环境测试
console.log('=== 测试子进程启动 ===');
console.log('process.execPath:', process.execPath);
console.log('__dirname:', __dirname);

// 测试脚本路径
const scriptPath = path.join(__dirname, 'dist', 'scraper', 'translator.js');
console.log('脚本路径:', scriptPath);
console.log('脚本是否存在:', fs.existsSync(scriptPath));

if (fs.existsSync(scriptPath)) {
  console.log('\n=== 尝试启动子进程 ===');
  
  const child = spawn(process.execPath, [scriptPath, '测试游戏'], {
    stdio: ['pipe', 'pipe', 'pipe'],
    env: {
      ...process.env,
      ELECTRON_RUN_AS_NODE: '1',
      LANG: 'zh_CN.UTF-8',
      LC_ALL: 'zh_CN.UTF-8',
      LC_CTYPE: 'zh_CN.UTF-8'
    }
  });

  child.on('error', (error) => {
    console.error('子进程启动失败:', error);
  });

  child.on('spawn', () => {
    console.log('子进程启动成功, PID:', child.pid);
  });

  child.stdout.on('data', (data) => {
    console.log('stdout:', data.toString());
  });

  child.stderr.on('data', (data) => {
    console.error('stderr:', data.toString());
  });

  child.on('close', (code) => {
    console.log('子进程退出，代码:', code);
  });

  // 5秒后终止测试
  setTimeout(() => {
    if (child && !child.killed) {
      child.kill();
      console.log('测试超时，终止子进程');
    }
  }, 5000);
} else {
  console.error('脚本文件不存在!');
}
