const fs = require('fs');
const path = require('path');

console.log('🔍 检查新打包版本的资源文件...');

const basePath = 'dist-release-new-test/win-unpacked/resources';

// 检查关键文件
const checks = [
  { name: 'app.asar', path: path.join(basePath, 'app.asar') },
  { name: 'app.asar.unpacked', path: path.join(basePath, 'app.asar.unpacked') },
  { name: 'Chromium浏览器', path: path.join(basePath, 'chromium', 'chrome.exe') },
  { name: '爬虫脚本目录', path: path.join(basePath, 'scraper') },
  { name: '主爬虫脚本', path: path.join(basePath, 'scraper', 'scraper.js') },
  { name: '详情爬虫脚本', path: path.join(basePath, 'scraper', 'detail-scraper.js') },
  { name: '搜索爬虫脚本', path: path.join(basePath, 'scraper', 'search-scraper.js') },
  { name: '翻译脚本', path: path.join(basePath, 'scraper', 'translator.js') }
];

console.log('\n📦 资源文件检查结果:');
checks.forEach(check => {
  const exists = fs.existsSync(check.path);
  console.log(`  ${exists ? '✅' : '❌'} ${check.name}: ${check.path}`);
  
  if (exists && check.name === '爬虫脚本目录') {
    try {
      const files = fs.readdirSync(check.path);
      console.log(`      包含文件: ${files.join(', ')}`);
    } catch (err) {
      console.log(`      读取目录失败: ${err.message}`);
    }
  }
});

// 检查解包目录内容
const unpackedPath = path.join(basePath, 'app.asar.unpacked');
if (fs.existsSync(unpackedPath)) {
  console.log('\n📂 app.asar.unpacked 目录内容:');
  try {
    const contents = fs.readdirSync(unpackedPath);
    contents.forEach(item => {
      const itemPath = path.join(unpackedPath, item);
      const isDir = fs.statSync(itemPath).isDirectory();
      console.log(`  ${isDir ? '📁' : '📄'} ${item}`);
      
      if (isDir && item === 'dist') {
        const distContents = fs.readdirSync(itemPath);
        distContents.forEach(distItem => {
          console.log(`    📁 ${distItem}`);
        });
      }
    });
  } catch (err) {
    console.log(`  读取失败: ${err.message}`);
  }
}

console.log('\n✅ 资源检查完成！');
console.log('\n💡 下一步: 手动启动应用程序测试功能');
console.log('   路径: dist-release-new-test/win-unpacked/游戏修改器盒子.exe');
